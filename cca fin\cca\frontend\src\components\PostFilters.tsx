import React from 'react';
import styled from 'styled-components';
import { Card, Input, theme } from '../styles/GlobalStyles';
import { PostFilters, FilterOption, Subreddit } from '../types';

const FiltersContainer = styled(Card)`
  padding: ${theme.spacing.lg};
  margin-bottom: ${theme.spacing.lg};
  background: ${theme.colors.surfaceBackground};
`;

const FiltersTitle = styled.h3`
  color: ${theme.colors.textPrimary};
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 ${theme.spacing.md} 0;
`;

const FiltersGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: ${theme.spacing.md};
  margin-bottom: ${theme.spacing.md};

  @media (max-width: ${theme.breakpoints.sm}) {
    grid-template-columns: 1fr;
  }
`;

const FilterGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing.xs};
`;

const FilterLabel = styled.label`
  font-size: 0.875rem;
  font-weight: 500;
  color: ${theme.colors.textPrimary};
`;

const FilterSelect = styled.select`
  padding: ${theme.spacing.sm};
  border: 1px solid ${theme.colors.border};
  border-radius: ${theme.borderRadius.md};
  background-color: ${theme.colors.background};
  color: ${theme.colors.textPrimary};
  font-size: 0.875rem;
  cursor: pointer;
  
  &:focus {
    outline: none;
    border-color: ${theme.colors.primary};
    box-shadow: 0 0 0 2px ${theme.colors.primary}20;
  }

  option {
    background-color: ${theme.colors.background};
    color: ${theme.colors.textPrimary};
  }
`;

const SearchContainer = styled.div`
  grid-column: 1 / -1;
`;

const ClearFiltersButton = styled.button`
  background: transparent;
  border: 1px solid ${theme.colors.border};
  color: ${theme.colors.textSecondary};
  padding: ${theme.spacing.sm} ${theme.spacing.md};
  border-radius: ${theme.borderRadius.md};
  font-size: 0.875rem;
  cursor: pointer;
  transition: all ${theme.transitions.fast};

  &:hover {
    background: ${theme.colors.surfaceBackground};
    border-color: ${theme.colors.primary};
    color: ${theme.colors.primary};
  }
`;

const FilterActions = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: ${theme.spacing.md};
  padding-top: ${theme.spacing.md};
  border-top: 1px solid ${theme.colors.border};
`;

const ResultsCount = styled.span`
  font-size: 0.875rem;
  color: ${theme.colors.textMuted};
`;

interface PostFiltersProps {
  filters: PostFilters;
  onFiltersChange: (filters: PostFilters) => void;
  communities?: Subreddit[];
  resultsCount?: number;
}

const sortOptions: FilterOption[] = [
  { value: 'newest', label: 'Newest First' },
  { value: 'oldest', label: 'Oldest First' },
  { value: 'popular', label: 'Most Popular' },
  { value: 'controversial', label: 'Most Controversial' },
];

const timeRangeOptions: FilterOption[] = [
  { value: 'all', label: 'All Time' },
  { value: 'today', label: 'Today' },
  { value: 'week', label: 'This Week' },
  { value: 'month', label: 'This Month' },
  { value: 'year', label: 'This Year' },
];

const PostFiltersComponent: React.FC<PostFiltersProps> = ({
  filters,
  onFiltersChange,
  communities = [],
  resultsCount,
}) => {
  const handleFilterChange = (key: keyof PostFilters, value: string) => {
    onFiltersChange({
      ...filters,
      [key]: value || undefined,
    });
  };

  const clearFilters = () => {
    onFiltersChange({
      sortBy: 'newest',
      timeRange: 'all',
      community: undefined,
      searchQuery: undefined,
    });
  };

  const hasActiveFilters = 
    filters.community || 
    filters.searchQuery || 
    filters.sortBy !== 'newest' || 
    filters.timeRange !== 'all';

  return (
    <FiltersContainer>
      <FiltersTitle>Filter & Sort Posts</FiltersTitle>
      
      <FiltersGrid>
        <FilterGroup>
          <FilterLabel htmlFor="sortBy">Sort By</FilterLabel>
          <FilterSelect
            id="sortBy"
            value={filters.sortBy}
            onChange={(e) => handleFilterChange('sortBy', e.target.value as any)}
          >
            {sortOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </FilterSelect>
        </FilterGroup>

        <FilterGroup>
          <FilterLabel htmlFor="timeRange">Time Range</FilterLabel>
          <FilterSelect
            id="timeRange"
            value={filters.timeRange}
            onChange={(e) => handleFilterChange('timeRange', e.target.value as any)}
          >
            {timeRangeOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </FilterSelect>
        </FilterGroup>

        <FilterGroup>
          <FilterLabel htmlFor="community">Community</FilterLabel>
          <FilterSelect
            id="community"
            value={filters.community || ''}
            onChange={(e) => handleFilterChange('community', e.target.value)}
          >
            <option value="">All Communities</option>
            {communities.map((community) => (
              <option key={community.id} value={community.name}>
                r/{community.name}
              </option>
            ))}
          </FilterSelect>
        </FilterGroup>

        <SearchContainer>
          <FilterGroup>
            <FilterLabel htmlFor="searchQuery">Search Posts</FilterLabel>
            <Input
              id="searchQuery"
              type="text"
              placeholder="Search by title or content..."
              value={filters.searchQuery || ''}
              onChange={(e) => handleFilterChange('searchQuery', e.target.value)}
            />
          </FilterGroup>
        </SearchContainer>
      </FiltersGrid>

      <FilterActions>
        <ResultsCount>
          {resultsCount !== undefined && `${resultsCount} post${resultsCount !== 1 ? 's' : ''} found`}
        </ResultsCount>
        
        {hasActiveFilters && (
          <ClearFiltersButton onClick={clearFilters}>
            Clear All Filters
          </ClearFiltersButton>
        )}
      </FilterActions>
    </FiltersContainer>
  );
};

export default PostFiltersComponent;