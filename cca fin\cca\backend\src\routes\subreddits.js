const express = require('express');
const { createSubreddit, getAllSubreddits, getSubredditPosts } = require('../controllers/subredditController');
const auth = require('../middleware/auth');
const { validateSubreddit } = require('../middleware/validation');

const router = express.Router();

// POST /api/subreddits (create subreddit) - requires auth
router.post('/', auth, validateSubreddit, createSubreddit);

// GET /api/subreddits (list all)
router.get('/', getAllSubreddits);

// GET /api/subreddits/:name/posts
router.get('/:name/posts', getSubredditPosts);

module.exports = router;