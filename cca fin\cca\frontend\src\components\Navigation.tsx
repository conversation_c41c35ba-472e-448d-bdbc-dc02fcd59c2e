import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { useAuth } from '../contexts/AuthContext';
import { Button, theme } from '../styles/GlobalStyles';
import ThemeToggle from './ThemeToggle';
import { MenuIcon, CloseIcon } from './Icons';

const NavContainer = styled.nav`
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid ${theme.colors.border};
  box-shadow: ${theme.shadows.lg};
  position: sticky;
  top: 0;
  z-index: 100;
  transition: all ${theme.transitions.normal};

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg,
      transparent,
      ${theme.colors.primary}60,
      ${theme.colors.secondary}60,
      transparent
    );
  }
`;

const NavContent = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 ${theme.spacing.md};
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;

  @media (min-width: ${theme.breakpoints.sm}) {
    padding: 0 ${theme.spacing.lg};
  }
`;

const Logo = styled(Link)`
  font-size: 1.75rem;
  font-weight: 800;
  background: linear-gradient(135deg, ${theme.colors.primary}, ${theme.colors.secondary});
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-decoration: none;
  transition: all ${theme.transitions.normal};
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, ${theme.colors.primary}, ${theme.colors.secondary});
    transition: width ${theme.transitions.normal};
  }

  &:hover {
    transform: translateY(-1px);
    filter: brightness(1.1);

    &::after {
      width: 100%;
    }
  }
`;

const NavLinks = styled.div<{ $isOpen: boolean }>`
  display: flex;
  align-items: center;
  gap: ${theme.spacing.md};

  @media (max-width: ${theme.breakpoints.md}) {
    position: fixed;
    top: 64px;
    left: 0;
    right: 0;
    background: ${theme.colors.cardBackground};
    border-bottom: 1px solid ${theme.colors.border};
    flex-direction: column;
    padding: ${theme.spacing.lg};
    transform: translateY(${({ $isOpen }) => ($isOpen ? '0' : '-100%')});
    transition: transform ${theme.transitions.normal};
    box-shadow: ${theme.shadows.md};
    backdrop-filter: blur(10px);
  }
`;

const NavLink = styled(Link)`
  color: ${theme.colors.textSecondary};
  font-weight: 600;
  text-decoration: none;
  padding: ${theme.spacing.sm} ${theme.spacing.lg};
  border-radius: ${theme.borderRadius.lg};
  transition: all ${theme.transitions.normal};
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
    transition: left ${theme.transitions.normal};
  }

  &:hover {
    color: ${theme.colors.primary};
    background: linear-gradient(135deg, ${theme.colors.primary}10, ${theme.colors.secondary}05);
    transform: translateY(-1px);
    box-shadow: ${theme.shadows.sm};

    &::before {
      left: 100%;
    }
  }

  &.active {
    color: ${theme.colors.primary};
    background: linear-gradient(135deg, ${theme.colors.primary}15, ${theme.colors.secondary}10);
    box-shadow: ${theme.shadows.sm};
  }
`;

const UserMenu = styled.div`
  position: relative;
  display: flex;
  align-items: center;
  gap: ${theme.spacing.sm};
`;

const UserButton = styled.button`
  display: flex;
  align-items: center;
  gap: ${theme.spacing.xs};
  padding: ${theme.spacing.sm};
  background: transparent;
  border: 1px solid ${theme.colors.border};
  border-radius: ${theme.borderRadius.md};
  color: ${theme.colors.textPrimary};
  font-weight: 500;
  transition: all ${theme.transitions.fast};

  &:hover {
    background: ${theme.colors.gray100};
    border-color: ${theme.colors.gray300};
  }
`;

const DropdownMenu = styled.div<{ $isOpen: boolean }>`
  position: absolute;
  top: 100%;
  right: 0;
  background: ${theme.colors.cardBackground};
  border: 1px solid ${theme.colors.border};
  border-radius: ${theme.borderRadius.md};
  box-shadow: ${theme.shadows.lg};
  min-width: 200px;
  padding: ${theme.spacing.sm};
  transform: translateY(${({ $isOpen }) => ($isOpen ? '8px' : '0')});
  opacity: ${({ $isOpen }) => ($isOpen ? '1' : '0')};
  visibility: ${({ $isOpen }) => ($isOpen ? 'visible' : 'hidden')};
  transition: all ${theme.transitions.fast};
  z-index: 1000;
  backdrop-filter: blur(10px);
`;

const DropdownItem = styled.button`
  width: 100%;
  display: flex;
  align-items: center;
  gap: ${theme.spacing.sm};
  padding: ${theme.spacing.sm} ${theme.spacing.md};
  background: transparent;
  border: none;
  border-radius: ${theme.borderRadius.sm};
  color: ${theme.colors.textPrimary};
  font-size: 0.875rem;
  text-align: left;
  transition: background ${theme.transitions.fast};

  &:hover {
    background: ${theme.colors.surfaceBackground};
  }
`;

const MobileMenuButton = styled.button`
  display: none;
  background: transparent;
  border: none;
  color: ${theme.colors.textPrimary};
  font-size: 1.25rem;
  padding: ${theme.spacing.sm};

  @media (max-width: ${theme.breakpoints.md}) {
    display: block;
  }
`;

const AuthButtons = styled.div`
  display: flex;
  align-items: center;
  gap: ${theme.spacing.sm};

  @media (max-width: ${theme.breakpoints.md}) {
    flex-direction: column;
    width: 100%;
  }
`;

const RightSection = styled.div`
  display: flex;
  align-items: center;
  gap: ${theme.spacing.sm};
`;

const Navigation: React.FC = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const { state, logout } = useAuth();
  const navigate = useNavigate();

  // Close menus when clicking outside
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('[data-user-menu]')) {
        setIsUserMenuOpen(false);
      }
      if (!target.closest('[data-mobile-menu]')) {
        setIsMobileMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleLogout = () => {
    logout();
    setIsUserMenuOpen(false);
    navigate('/');
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const toggleUserMenu = () => {
    setIsUserMenuOpen(!isUserMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  const closeUserMenu = () => {
    setIsUserMenuOpen(false);
  };



  return (
    <NavContainer>
      <NavContent>
        <Logo to="/" onClick={closeMobileMenu}>
          TalkHub
        </Logo>

        <NavLinks $isOpen={isMobileMenuOpen}>
          {state.isAuthenticated && (
            <>
              <NavLink to="/" onClick={closeMobileMenu}>
                Home
              </NavLink>
              <NavLink to="/communities" onClick={closeMobileMenu}>
                Communities
              </NavLink>
              <Button
                as={Link}
                to="/create-post"
                size="sm"
                onClick={closeMobileMenu}
              >
                Create Post
              </Button>
            </>
          )}

          {state.isAuthenticated ? (
            <UserMenu data-user-menu>
              <UserButton onClick={toggleUserMenu}>
                {state.user?.username}
              </UserButton>
              <DropdownMenu $isOpen={isUserMenuOpen}>
                <DropdownItem onClick={() => { navigate('/profile'); closeUserMenu(); }}>
                  Profile
                </DropdownItem>
                {state.user?.role === 'admin' && (
                  <DropdownItem onClick={() => { navigate('/admin'); closeUserMenu(); }}>
                    Admin Panel
                  </DropdownItem>
                )}
                <DropdownItem onClick={handleLogout}>
                  Logout
                </DropdownItem>
              </DropdownMenu>
            </UserMenu>
          ) : (
            <AuthButtons>
              <Button
                as={Link}
                to="/login"
                variant="outline"
                size="sm"
                onClick={closeMobileMenu}
              >
                Sign In
              </Button>
              <Button
                as={Link}
                to="/register"
                size="sm"
                onClick={closeMobileMenu}
              >
                Sign Up
              </Button>
            </AuthButtons>
          )}
        </NavLinks>

        <RightSection>
          <ThemeToggle />
          <MobileMenuButton data-mobile-menu onClick={toggleMobileMenu}>
            {isMobileMenuOpen ? <CloseIcon size={20} /> : <MenuIcon size={20} />}
          </MobileMenuButton>
        </RightSection>
      </NavContent>
    </NavContainer>
  );
};

export default Navigation;
