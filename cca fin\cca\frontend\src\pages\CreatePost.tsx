import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import styled from 'styled-components';
import { Container, Card, Button, Input, TextArea, ErrorMessage, LoadingSpinner, theme } from '../styles/GlobalStyles';
import { CreatePostRequest, Subreddit } from '../types';
import { apiService } from '../services/api';

const CreateContainer = styled(Container)`
  padding-top: ${theme.spacing.xl};
  padding-bottom: ${theme.spacing.xl};
  max-width: 600px;
`;

const Title = styled.h1`
  color: ${theme.colors.textPrimary};
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: ${theme.spacing.xl};
  text-align: center;
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing.lg};
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing.xs};
`;

const Label = styled.label`
  font-weight: 500;
  color: ${theme.colors.textPrimary};
  font-size: 0.875rem;
`;

const HelpText = styled.div`
  font-size: 0.75rem;
  color: ${theme.colors.textMuted};
  margin-top: ${theme.spacing.xs};
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: ${theme.spacing.md};
  justify-content: flex-end;
  margin-top: ${theme.spacing.lg};

  @media (max-width: ${theme.breakpoints.sm}) {
    flex-direction: column;
  }
`;

const SubredditSelector = styled.select`
  padding: ${theme.spacing.sm};
  border: 1px solid ${theme.colors.border};
  border-radius: ${theme.borderRadius.md};
  background-color: ${theme.colors.background};
  color: ${theme.colors.textPrimary};
  font-size: 0.875rem;
  cursor: pointer;
  min-height: 40px;
  
  &:focus {
    outline: none;
    border-color: ${theme.colors.primary};
    box-shadow: 0 0 0 2px ${theme.colors.primary}20;
  }

  &:disabled {
    background-color: ${theme.colors.surfaceBackground};
    cursor: not-allowed;
    opacity: 0.6;
  }

  option {
    background-color: ${theme.colors.background};
    color: ${theme.colors.textPrimary};
    padding: ${theme.spacing.sm};
  }
`;

const CreatePost: React.FC = () => {
  const { subredditName } = useParams<{ subredditName?: string }>();
  const [formData, setFormData] = useState<CreatePostRequest>({
    title: '',
    body: '',
    subredditId: '',
  });

  const [subreddits, setSubreddits] = useState<Subreddit[]>([]);
  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingSubreddits, setIsLoadingSubreddits] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  useEffect(() => {
    // This effect handles setting the subredditId only after subreddits have been fetched.
    if (subredditName && subreddits.length > 0) {
        const subreddit = subreddits.find(s => s.name === subredditName);
        if (subreddit) {
            console.log(`Found subreddit, setting ID: ${subreddit.id}`);
            setFormData(prev => ({ ...prev, subredditId: subreddit.id }));
        } else {
            // Handle case where URL has a subreddit name, but it's not found in the fetched list
            console.error(`Subreddit with name "${subredditName}" not found.`);
            // You might want to display an error or redirect here
        }
    } 
    else {
        // Clear subredditId if no subreddit name is in the URL
        setFormData(prev => ({ ...prev, subredditId: '' }));
      }
  }, [subredditName, subreddits]);

// Add a separate useEffect to fetch subreddits
useEffect(() => {
    fetchSubreddits();
}, []); // Empty dependency array ensures it runs only once on mount


  const fetchSubreddits = async () => {
    try {
      setIsLoadingSubreddits(true);
      const response = await apiService.getSubreddits();
      setSubreddits(response.subreddits);
    } catch (err: any) {
      setError('Failed to load communities');
    } finally {
      setIsLoadingSubreddits(false);
    }
  };

  const validateForm = (): boolean => {
  const errors: { [key: string]: string } = {};
  const minTitleLength = 10; // This should match your backend's minimum length for titles
  const maxTitleLength = 300;

  if (!formData.title.trim()) {
    errors.title = 'Title cannot be empty';
  } else if (formData.title.length < minTitleLength) {
    errors.title = `Title must be at least ${minTitleLength} characters`;
  } else if (formData.title.length > maxTitleLength) {
    errors.title = `Title must be less than ${maxTitleLength} characters`;
  }

  if (!formData.subredditId || formData.subredditId === '') {
    errors.subredditId = 'Please select a community';
  }

  setFormErrors(errors);
  return Object.keys(errors).length === 0;
};

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    console.log(`Field name: ${name}, value: ${value}`);
    
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));

    // Clear field error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: '',
      }));
    }

    // Clear general error
    if (error) {
      setError(null);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    console.log('Form submitted with data:', formData);
    
    if (!validateForm()) {
      console.log('Form validation failed');
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      console.log('Sending post data to API:', formData);
      const response = await apiService.createPost(formData);
      console.log('Post created successfully:', response);
      const selectedSubreddit = subreddits.find(s => s.id === formData.subredditId);
      if (selectedSubreddit) {
        navigate(`/r/${selectedSubreddit.name}`);
      } else {
        navigate('/');
      }
    } catch (err: any) {
      console.error('Failed to create post:', err);
      setError(err.response?.data?.error || 'Failed to create post');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    if (subredditName) {
      navigate(`/r/${subredditName}`);
    } else {
      navigate('/');
    }
  };

  if (isLoadingSubreddits) {
    return (
      <CreateContainer>
        <Card>
          <div style={{ textAlign: 'center', padding: theme.spacing.xl }}>
            <LoadingSpinner />
            <div style={{ marginTop: theme.spacing.md }}>Loading communities...</div>
          </div>
        </Card>
      </CreateContainer>
    );
  }

  return (
    <CreateContainer>
      <Card>
        <Title>Create a Post</Title>
        
        <Form onSubmit={handleSubmit}>
          <FormGroup>
            <Label htmlFor="subredditId">Community</Label>
            <SubredditSelector
              id="subredditId"
              name="subredditId"
              value={formData.subredditId}
              onChange={handleInputChange}
              disabled={!!subredditName}
            >
              <option value="">Select a community</option>
              {subreddits.map((subreddit) => (
                <option key={subreddit.id} value={subreddit.id}>
                  r/{subreddit.name}
                </option>
              ))}
            </SubredditSelector>
            {formErrors.subredditId && <ErrorMessage>{formErrors.subredditId}</ErrorMessage>}
          </FormGroup>

          <FormGroup>
            <Label htmlFor="title">Title</Label>
            <Input
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              placeholder="Enter post title"
              maxLength={300}
            />
            <HelpText>
              {formData.title.length}/300 characters
            </HelpText>
            {formErrors.title && <ErrorMessage>{formErrors.title}</ErrorMessage>}
          </FormGroup>

          <FormGroup>
            <Label htmlFor="body">Content (Optional)</Label>
            <TextArea
              id="body"
              name="body"
              value={formData.body}
              onChange={handleInputChange}
              placeholder="What's on your mind?"
              rows={8}
            />
            <HelpText>
              Share your thoughts, ask questions, or start a discussion
            </HelpText>
          </FormGroup>

          {error && <ErrorMessage>{error}</ErrorMessage>}

          <ButtonGroup>
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading || !formData.title.trim() || !formData.subredditId}
            >
              {isLoading ? <LoadingSpinner /> : 'Create Post'}
            </Button>
          </ButtonGroup>
        </Form>
      </Card>
    </CreateContainer>
  );
};

export default CreatePost;
