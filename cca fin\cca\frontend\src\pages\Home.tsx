import React, { useState, useEffect, useMemo } from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import { Container, Card, Button, ErrorMessage, LoadingSpinner, theme } from '../styles/GlobalStyles';
import { Post, PostFilters, Subreddit } from '../types';
import { apiService } from '../services/api';
import PostCard from '../components/PostCard';
import PostFiltersComponent from '../components/PostFilters';

const HomeContainer = styled(Container)`
  padding-top: ${theme.spacing.xl};
  padding-bottom: ${theme.spacing.xl};
`;

const WelcomeSection = styled(Card)`
  margin-bottom: ${theme.spacing.xl};
  padding: ${theme.spacing.xl};
  text-align: center;
  background: linear-gradient(135deg, ${theme.colors.primary}10, ${theme.colors.secondary}10);
  border: 1px solid ${theme.colors.primary}20;
`;

const WelcomeTitle = styled.h1`
  color: ${theme.colors.textPrimary};
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 ${theme.spacing.md} 0;
`;

const WelcomeSubtitle = styled.p`
  color: ${theme.colors.textSecondary};
  font-size: 1.125rem;
  line-height: 1.6;
  margin: 0 0 ${theme.spacing.lg} 0;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: ${theme.spacing.md};
  justify-content: center;
  flex-wrap: wrap;
`;

const PostsSection = styled.div`
  margin-top: ${theme.spacing.xl};
`;

const SectionTitle = styled.h2`
  color: ${theme.colors.textPrimary};
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 ${theme.spacing.lg} 0;
`;

const PostsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: ${theme.spacing.lg};

  @media (max-width: ${theme.breakpoints.sm}) {
    grid-template-columns: 1fr;
  }
`;


const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${theme.spacing.xl};
  gap: ${theme.spacing.md};
  color: ${theme.colors.textSecondary};
`;

const EmptyState = styled.div`
  text-align: center;
  padding: ${theme.spacing.xl};
  color: ${theme.colors.textSecondary};

  h3 {
    color: ${theme.colors.textPrimary};
    margin-bottom: ${theme.spacing.md};
  }

  p {
    margin-bottom: ${theme.spacing.lg};
    line-height: 1.6;
  }
`;
const Home: React.FC = () => {
  const [posts, setPosts] = useState<Post[]>([]);
  const [communities, setCommunities] = useState<Subreddit[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<PostFilters>({
    sortBy: 'newest',
    timeRange: 'all',
  });

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const [postsResponse, communitiesResponse] = await Promise.all([
        apiService.getAllPosts(),
        apiService.getSubreddits(),
      ]);
      setPosts(postsResponse.posts);
      setCommunities(communitiesResponse.subreddits);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to load data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleVote = (postId: string, value: 1 | -1) => {
    // Update the post in the local state
    setPosts(prevPosts => 
      prevPosts.map(post => 
        post.id === postId 
          ? { 
              ...post, 
              userVote: post.userVote === value ? null : value,
              score: (post.score || 0) + (post.userVote === value ? -value : value - (post.userVote || 0))
            }
          : post
      )
    );
  };

  // Filter and sort posts based on current filters
  const filteredPosts = useMemo(() => {
    let filtered = [...posts];

    // Filter by community
    if (filters.community) {
      filtered = filtered.filter(post => post.subreddit.name === filters.community);
    }

    // Filter by search query
    if (filters.searchQuery) {
      const query = filters.searchQuery.toLowerCase();
      filtered = filtered.filter(post => 
        post.title.toLowerCase().includes(query) ||
        (post.body && post.body.toLowerCase().includes(query))
      );
    }

    // Filter by time range
    if (filters.timeRange !== 'all') {
      const now = new Date();
      const timeRanges = {
        today: 1,
        week: 7,
        month: 30,
        year: 365,
      };
      const daysAgo = timeRanges[filters.timeRange as keyof typeof timeRanges];
      const cutoffDate = new Date(now.getTime() - daysAgo * 24 * 60 * 60 * 1000);
      
      filtered = filtered.filter(post => new Date(post.createdAt) >= cutoffDate);
    }

    // Sort posts
    filtered.sort((a, b) => {
      switch (filters.sortBy) {
        case 'newest':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        case 'oldest':
          return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        case 'popular':
          return (b.score || 0) - (a.score || 0);
        case 'controversial':
          // Sort by posts with similar upvotes and downvotes (controversial)
          const aControversy = Math.min(a.upvotes || 0, a.downvotes || 0);
          const bControversy = Math.min(b.upvotes || 0, b.downvotes || 0);
          return bControversy - aControversy;
        default:
          return 0;
      }
    });

    return filtered;
  }, [posts, filters]);

  if (isLoading) {
    return (
      <HomeContainer>
        <LoadingContainer>
          <LoadingSpinner />
          <div>Loading latest posts...</div>
        </LoadingContainer>
      </HomeContainer>
    );
  }

  return (
    <HomeContainer>
      <WelcomeSection>
        <WelcomeTitle>Welcome to TalkHub</WelcomeTitle>
        <WelcomeSubtitle>
          Your community-driven platform for meaningful discussions.
          Connect with like-minded people, share your thoughts, and discover new perspectives
          across diverse communities.
        </WelcomeSubtitle>
        <ActionButtons>
          <Button as={Link} to="/communities">
            Explore Communities
          </Button>
          <Button as={Link} to="/create-post" variant="outline">
            Create Post
          </Button>
        </ActionButtons>
      </WelcomeSection>

      <PostsSection>
        <SectionTitle>Community Posts</SectionTitle>

        <PostFiltersComponent
          filters={filters}
          onFiltersChange={setFilters}
          communities={communities}
          resultsCount={filteredPosts.length}
        />

        {error && (
          <ErrorMessage>
            {error}
            <Button
              variant="ghost"
              size="sm"
              onClick={fetchData}
              style={{ marginLeft: theme.spacing.md }}
            >
              Try Again
            </Button>
          </ErrorMessage>
        )}

        {filteredPosts.length === 0 && !isLoading && !error ? (
          <EmptyState>
            <h3>No posts found</h3>
            <p>
              {filters.searchQuery || filters.community || filters.timeRange !== 'all'
                ? 'Try adjusting your filters or search terms.'
                : 'Be the first to start a conversation! Create a post or explore communities to get started.'
              }
            </p>
            {!filters.searchQuery && !filters.community && filters.timeRange === 'all' && (
              <Button as={Link} to="/create-post" style={{ marginTop: theme.spacing.lg }}>
                Create First Post
              </Button>
            )}
          </EmptyState>
        ) : (
          <PostsGrid>
            {filteredPosts.map((post) => (
              <PostCard
                key={post.id}
                post={post}
                onVote={handleVote}
              />
            ))}
          </PostsGrid>
        )}
      </PostsSection>
    </HomeContainer>
  );
};

export default Home;
