// MongoDB initialization script for TalkHub
// This script creates the database and sets up initial collections

// Switch to the talkhub database
db = db.getSiblingDB('talkhub');

// Create collections with validation
db.createCollection('users', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['username', 'email', 'password'],
      properties: {
        username: {
          bsonType: 'string',
          minLength: 3,
          maxLength: 30,
          pattern: '^[a-zA-Z0-9]+$'
        },
        email: {
          bsonType: 'string',
          pattern: '^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$'
        },
        password: {
          bsonType: 'string',
          minLength: 6,
          maxLength: 100
        }
      }
    }
  }
});

db.createCollection('subreddits', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['name'],
      properties: {
        name: {
          bsonType: 'string',
          minLength: 3,
          maxLength: 50,
          pattern: '^[a-zA-Z0-9_]+$'
        },
        description: {
          bsonType: 'string',
          maxLength: 500
        }
      }
    }
  }
});

db.createCollection('posts', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['title', 'userId', 'subredditId'],
      properties: {
        title: {
          bsonType: 'string',
          minLength: 5,
          maxLength: 300
        },
        body: {
          bsonType: 'string'
        },
        userId: {
          bsonType: 'objectId'
        },
        subredditId: {
          bsonType: 'objectId'
        }
      }
    }
  }
});

db.createCollection('comments', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['content', 'postId', 'userId'],
      properties: {
        content: {
          bsonType: 'string',
          minLength: 1,
          maxLength: 10000
        },
        postId: {
          bsonType: 'objectId'
        },
        userId: {
          bsonType: 'objectId'
        },
        parentCommentId: {
          bsonType: ['objectId', 'null']
        }
      }
    }
  }
});

db.createCollection('votes', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['userId', 'postId', 'value'],
      properties: {
        userId: {
          bsonType: 'objectId'
        },
        postId: {
          bsonType: 'objectId'
        },
        value: {
          bsonType: 'int',
          enum: [-1, 1]
        }
      }
    }
  }
});

// Create indexes for better performance
db.users.createIndex({ email: 1 }, { unique: true });
db.users.createIndex({ username: 1 }, { unique: true });

db.subreddits.createIndex({ name: 1 }, { unique: true });

db.posts.createIndex({ subredditId: 1 });
db.posts.createIndex({ userId: 1 });
db.posts.createIndex({ createdAt: -1 });

db.comments.createIndex({ postId: 1 });
db.comments.createIndex({ userId: 1 });
db.comments.createIndex({ parentCommentId: 1 });

db.votes.createIndex({ userId: 1, postId: 1 }, { unique: true });
db.votes.createIndex({ postId: 1 });

print('TalkHub MongoDB database initialized successfully!');