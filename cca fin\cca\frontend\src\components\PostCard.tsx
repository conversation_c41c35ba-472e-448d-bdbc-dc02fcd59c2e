import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { formatDistanceToNow } from 'date-fns';
import styled from 'styled-components';
import { Card, theme } from '../styles/GlobalStyles';
import { Post } from '../types';
import { apiService } from '../services/api';

const PostCardContainer = styled(Card)`
  padding: ${theme.spacing.lg};
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  cursor: pointer;
  position: relative;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
`;

const PostContent = styled.div`
  margin-bottom: ${theme.spacing.md};
`;

const PostTitle = styled.h3`
  color: ${theme.colors.textPrimary};
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 ${theme.spacing.sm} 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
`;

const PostBody = styled.p`
  color: ${theme.colors.textSecondary};
  font-size: 0.875rem;
  line-height: 1.5;
  margin: 0 0 ${theme.spacing.md} 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
`;

const PostFooter = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: ${theme.spacing.md};
`;

const PostMeta = styled.div`
  display: flex;
  align-items: center;
  font-size: 0.75rem;
  color: ${theme.colors.textMuted};
  flex-wrap: wrap;
  gap: ${theme.spacing.sm};
  flex: 1;
`;

const MetaItem = styled.span`
  display: flex;
  align-items: center;
  gap: ${theme.spacing.xs};
`;

const CommunityTag = styled.span`
  background: ${theme.colors.primary}20;
  color: ${theme.colors.primary};
  padding: ${theme.spacing.xs} ${theme.spacing.sm};
  border-radius: ${theme.borderRadius.sm};
  font-size: 0.75rem;
  font-weight: 500;
`;

const VotingSection = styled.div`
  display: flex;
  align-items: center;
  gap: ${theme.spacing.xs};
  background: ${theme.colors.surfaceBackground};
  border-radius: ${theme.borderRadius.md};
  padding: ${theme.spacing.xs};
`;

const VoteButton = styled.button<{ $active?: boolean; $type: 'up' | 'down' }>`
  background: ${({ $active, $type }) => 
    $active 
      ? $type === 'up' 
        ? theme.colors.success 
        : theme.colors.danger
      : 'transparent'
  };
  color: ${({ $active, $type }) => 
    $active 
      ? 'white'
      : $type === 'up' 
        ? theme.colors.success 
        : theme.colors.danger
  };
  border: 1px solid ${({ $type }) => 
    $type === 'up' ? theme.colors.success : theme.colors.danger
  };
  border-radius: ${theme.borderRadius.sm};
  padding: ${theme.spacing.xs};
  cursor: pointer;
  transition: all ${theme.transitions.fast};
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  font-size: 0.875rem;

  &:hover {
    background: ${({ $type }) => 
      $type === 'up' 
        ? theme.colors.success + '20' 
        : theme.colors.danger + '20'
    };
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const VoteScore = styled.span`
  font-size: 0.875rem;
  font-weight: 600;
  color: ${theme.colors.textPrimary};
  min-width: 30px;
  text-align: center;
`;

const InteractionBar = styled.div`
  display: flex;
  align-items: center;
  gap: ${theme.spacing.md};
  margin-top: ${theme.spacing.sm};
  padding-top: ${theme.spacing.sm};
  border-top: 1px solid ${theme.colors.border};
`;

const InteractionButton = styled.button`
  background: transparent;
  border: none;
  color: ${theme.colors.textMuted};
  font-size: 0.75rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: ${theme.spacing.xs};
  padding: ${theme.spacing.xs} ${theme.spacing.sm};
  border-radius: ${theme.borderRadius.sm};
  transition: all ${theme.transitions.fast};

  &:hover {
    background: ${theme.colors.surfaceBackground};
    color: ${theme.colors.textSecondary};
  }
`;

interface PostCardProps {
  post: Post;
  onVote?: (postId: string, value: 1 | -1) => void;
  showFullContent?: boolean;
}

const PostCard: React.FC<PostCardProps> = ({ post, onVote, showFullContent = false }) => {
  const [isVoting, setIsVoting] = useState(false);
  const [currentVote, setCurrentVote] = useState<1 | -1 | null>(post.userVote || null);
  const [score, setScore] = useState(post.score || 0);

  const handleVote = async (e: React.MouseEvent, value: 1 | -1) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (isVoting) return;

    try {
      setIsVoting(true);
      
      // Optimistic update
      const newVote = currentVote === value ? null : value;
      const scoreDiff = newVote === null 
        ? (currentVote === 1 ? -1 : 1)
        : newVote === 1 
          ? (currentVote === -1 ? 2 : 1)
          : (currentVote === 1 ? -2 : -1);
      
      setCurrentVote(newVote);
      setScore(prev => prev + scoreDiff);

      // Call API
      await apiService.voteOnPost(post.id, { value });
      
      // Call parent callback if provided
      if (onVote) {
        onVote(post.id, value);
      }
    } catch (error) {
      // Revert optimistic update on error
      setCurrentVote(post.userVote || null);
      setScore(post.score || 0);
      console.error('Failed to vote:', error);
    } finally {
      setIsVoting(false);
    }
  };

  const handleCardClick = (e: React.MouseEvent) => {
    // Don't navigate if clicking on interactive elements
    const target = e.target as HTMLElement;
    if (target.closest('button') || target.closest('a')) {
      return;
    }
  };

  return (
    <Link
      to={`/r/${post.subreddit.name}/posts/${post.id}`}
      style={{ textDecoration: 'none' }}
      onClick={handleCardClick}
    >
      <PostCardContainer>
        <PostContent>
          <PostTitle>{post.title}</PostTitle>
          {post.body && (
            <PostBody style={showFullContent ? { 
              display: 'block', 
              WebkitLineClamp: 'unset',
              overflow: 'visible' 
            } : {}}>
              {post.body}
            </PostBody>
          )}
        </PostContent>

        <PostFooter>
          <PostMeta>
            <MetaItem>
              <CommunityTag>r/{post.subreddit.name}</CommunityTag>
            </MetaItem>
            <MetaItem>
              <span>by u/{post.author.username}</span>
            </MetaItem>
            <MetaItem>
              <span>{formatDistanceToNow(new Date(post.createdAt))} ago</span>
            </MetaItem>
          </PostMeta>

          <VotingSection>
            <VoteButton
              $type="up"
              $active={currentVote === 1}
              onClick={(e) => handleVote(e, 1)}
              disabled={isVoting}
              title="Upvote"
            >
              ↑
            </VoteButton>
            <VoteScore>{score}</VoteScore>
            <VoteButton
              $type="down"
              $active={currentVote === -1}
              onClick={(e) => handleVote(e, -1)}
              disabled={isVoting}
              title="Downvote"
            >
              ↓
            </VoteButton>
          </VotingSection>
        </PostFooter>

        <InteractionBar>
          <InteractionButton>
            💬 {post.commentsCount || 0} comments
          </InteractionButton>
          <InteractionButton>
            📤 Share
          </InteractionButton>
          <InteractionButton>
            🔖 Save
          </InteractionButton>
        </InteractionBar>
      </PostCardContainer>
    </Link>
  );
};

export default PostCard;