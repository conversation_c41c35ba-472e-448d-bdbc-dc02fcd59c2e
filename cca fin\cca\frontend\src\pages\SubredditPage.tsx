import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import styled from 'styled-components';
import { Contain<PERSON>, Card, But<PERSON>, LoadingSpinner, theme } from '../styles/GlobalStyles';
import { Post, Subreddit } from '../types';
import { apiService } from '../services/api';
import { formatDistanceToNow } from 'date-fns';

const SubredditContainer = styled(Container)`
  padding-top: ${theme.spacing.xl};
  padding-bottom: ${theme.spacing.xl};
`;

const SubredditHeader = styled.div`
  background: ${theme.colors.white};
  border-radius: ${theme.borderRadius.lg};
  padding: ${theme.spacing.xl};
  margin-bottom: ${theme.spacing.xl};
  box-shadow: ${theme.shadows.sm};
  border: 1px solid ${theme.colors.border};
`;

const SubredditTitle = styled.h1`
  color: ${theme.colors.textPrimary};
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 ${theme.spacing.sm} 0;
`;

const SubredditDescription = styled.p`
  color: ${theme.colors.textSecondary};
  margin: 0 0 ${theme.spacing.lg} 0;
  line-height: 1.6;
`;

const PostsHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${theme.spacing.lg};
  flex-wrap: wrap;
  gap: ${theme.spacing.md};
`;

const PostsTitle = styled.h2`
  color: ${theme.colors.textPrimary};
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
`;

const PostsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing.md};
`;

const PostCard = styled(Card)`
  display: flex;
  gap: ${theme.spacing.md};
  transition: all ${theme.transitions.fast};
  
  &:hover {
    box-shadow: ${theme.shadows.md};
  }
`;

const VoteSection = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: ${theme.spacing.xs};
  min-width: 40px;
`;

const VoteButton = styled.button<{ active?: boolean; type?: 'up' | 'down' }>`
  background: transparent;
  border: none;
  color: ${({ active, type }) => 
    active 
      ? type === 'up' 
        ? theme.colors.success 
        : theme.colors.danger
      : theme.colors.textMuted
  };
  padding: ${theme.spacing.xs};
  border-radius: ${theme.borderRadius.sm};
  transition: all ${theme.transitions.fast};
  cursor: pointer;

  &:hover {
    background: ${theme.colors.gray100};
    color: ${({ type }) => 
      type === 'up' ? theme.colors.success : theme.colors.danger
    };
  }
`;

const VoteCount = styled.div`
  font-weight: 600;
  font-size: 0.875rem;
  color: ${theme.colors.textPrimary};
`;

const PostContent = styled.div`
  flex: 1;
`;

const PostTitle = styled.h3`
  color: ${theme.colors.textPrimary};
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 ${theme.spacing.sm} 0;
  line-height: 1.4;
`;

const PostBody = styled.p`
  color: ${theme.colors.textSecondary};
  margin: 0 0 ${theme.spacing.md} 0;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
`;

const PostMeta = styled.div`
  display: flex;
  align-items: center;
  gap: ${theme.spacing.md};
  color: ${theme.colors.textMuted};
  font-size: 0.875rem;
  flex-wrap: wrap;
`;

const MetaItem = styled.div`
  display: flex;
  align-items: center;
  gap: ${theme.spacing.xs};
`;

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  flex-direction: column;
  gap: ${theme.spacing.md};
`;

const EmptyState = styled.div`
  text-align: center;
  padding: ${theme.spacing.xxl};
  color: ${theme.colors.textSecondary};
`;

const ErrorMessage = styled.div`
  background: ${theme.colors.danger}10;
  color: ${theme.colors.danger};
  padding: ${theme.spacing.md};
  border-radius: ${theme.borderRadius.md};
  margin-bottom: ${theme.spacing.lg};
  text-align: center;
`;

const SubredditPage: React.FC = () => {
  const { subredditName } = useParams<{ subredditName: string }>();
  const [subreddit, setSubreddit] = useState<Subreddit | null>(null);
  const [posts, setPosts] = useState<Post[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (subredditName) {
      fetchSubredditData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [subredditName]);

  const fetchSubredditData = async () => {
    if (!subredditName) return;

    try {
      setIsLoading(true);
      setError(null);
      const response = await apiService.getSubredditPosts(subredditName);
      setSubreddit(response.subreddit);
      setPosts(response.posts);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to load subreddit');
    } finally {
      setIsLoading(false);
    }
  };

  const handleVote = async (postId: string, value: 1 | -1) => {
    try {
      await apiService.voteOnPost(postId, { value });
      // Refresh posts to get updated vote counts
      fetchSubredditData();
    } catch (err: any) {
      console.error('Failed to vote:', err);
    }
  };

  if (isLoading) {
    return (
      <SubredditContainer>
        <LoadingContainer>
          <LoadingSpinner />
          <div>Loading subreddit...</div>
        </LoadingContainer>
      </SubredditContainer>
    );
  }

  if (error || !subreddit) {
    return (
      <SubredditContainer>
        <ErrorMessage>
          {error || 'Subreddit not found'}
          <Button
            variant="ghost"
            size="sm"
            onClick={fetchSubredditData}
            style={{ marginLeft: theme.spacing.md }}
          >
            Try Again
          </Button>
        </ErrorMessage>
      </SubredditContainer>
    );
  }

  return (
    <SubredditContainer>
      <SubredditHeader>
        <SubredditTitle>r/{subreddit.name}</SubredditTitle>
        <SubredditDescription>
          {subreddit.description || 'No description available'}
        </SubredditDescription>
        <Button as={Link} to={`/r/${subreddit.name}/create-post`}>
          Create Post
        </Button>
      </SubredditHeader>

      <PostsHeader>
        <PostsTitle>Posts</PostsTitle>
      </PostsHeader>

      {posts.length === 0 ? (
        <EmptyState>
          <h3>No posts yet</h3>
          <p>Be the first to post in this community!</p>
          <Button
            as={Link}
            to={`/r/${subreddit.name}/create-post`}
            style={{ marginTop: theme.spacing.lg }}
          >
            Create Post
          </Button>
        </EmptyState>
      ) : (
        <PostsList>
          {posts.map((post) => (
            <PostCard key={post.id}>
              <VoteSection>
                <VoteButton
                  type="up"
                  onClick={() => handleVote(post.id, 1)}
                >
                </VoteButton>
                <VoteCount>{post.score || 0}</VoteCount>
                <VoteButton
                  type="down"
                  onClick={() => handleVote(post.id, -1)}
                >
                </VoteButton>
              </VoteSection>

              <PostContent>
                <PostTitle
                  as={Link}
                  to={`/r/${subreddit.name}/posts/${post.id}`}
                >
                  {post.title}
                </PostTitle>
                {post.body && <PostBody>{post.body}</PostBody>}
                
                <PostMeta>
                  <MetaItem>
                    <span>u/{post.author.username}</span>
                  </MetaItem>
                  <MetaItem>
                    <span>{formatDistanceToNow(new Date(post.createdAt))} ago</span>
                  </MetaItem>
                  <MetaItem>
                    <span>Comments</span>
                  </MetaItem>
                </PostMeta>
              </PostContent>
            </PostCard>
          ))}
        </PostsList>
      )}
    </SubredditContainer>
  );
};

export default SubredditPage;
