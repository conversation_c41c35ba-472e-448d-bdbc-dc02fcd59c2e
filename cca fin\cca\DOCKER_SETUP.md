# TalkHub Docker Setup Guide

## 🚀 Complete Docker Containerization Instructions

### Prerequisites
- Docker Desktop installed and running
- At least 4GB RAM available for Docker
- Ports 3000, 5000, and 5432 available

## Step-by-Step Setup

### 1. Prepare the Environment
```bash
# Navigate to the project root
cd c:\Users\<USER>\OneDrive\Desktop\cc

# Verify Docker is running
docker --version
docker-compose --version
```

### 2. Build and Start All Services
```bash
# Build and start all services (database, backend, frontend)
docker-compose up --build

# Or run in detached mode (background)
docker-compose up --build -d
```

### 3. Verify Services are Running
```bash
# Check service status
docker-compose ps

# Expected output:
# talkhub-db        running   0.0.0.0:5432->5432/tcp
# talkhub-backend   running   0.0.0.0:5000->5000/tcp  
# talkhub-frontend  running   0.0.0.0:3000->3000/tcp
```

### 4. Access the Application
- **Frontend (React App)**: http://localhost:3000
- **Backend API**: http://localhost:5000/api/health
- **Database**: localhost:5432 (use pgAdmin or similar)

### 5. Test the Application
1. Open http://localhost:3000 in your browser
2. Click "Sign Up" to create a new account
3. Fill in the registration form and submit
4. Login with your credentials
5. Create a community and start posting!

## Service Details

### Database (PostgreSQL)
- **Container**: talkhub-db
- **Port**: 5432
- **Credentials**: postgres/postgres
- **Database**: talkhub

### Backend (Node.js API)
- **Container**: talkhub-backend
- **Port**: 5000
- **Health Check**: http://localhost:5000/api/health
- **Features**: JWT auth, rate limiting, input validation

### Frontend (React + Nginx)
- **Container**: talkhub-frontend
- **Port**: 3000
- **Health Check**: http://localhost:3000/health
- **Features**: TypeScript, responsive design, modern UI

## Docker Commands Reference

### Basic Operations
```bash
# Start services
docker-compose up

# Start in background
docker-compose up -d

# Stop services
docker-compose down

# Stop and remove volumes (clears database)
docker-compose down -v

# Rebuild services
docker-compose up --build

# View logs
docker-compose logs

# View logs for specific service
docker-compose logs backend
docker-compose logs frontend
docker-compose logs db
```

### Development Commands
```bash
# Rebuild specific service
docker-compose build backend
docker-compose build frontend

# Restart specific service
docker-compose restart backend

# Execute commands in running container
docker-compose exec backend npm run test
docker-compose exec db psql -U postgres -d talkhub

# View real-time logs
docker-compose logs -f backend
```

### Troubleshooting Commands
```bash
# Check service health
docker-compose exec backend curl http://localhost:5000/api/health
docker-compose exec frontend curl http://localhost:3000/health

# Access database shell
docker-compose exec db psql -U postgres -d talkhub

# Check container resource usage
docker stats

# Clean up everything
docker-compose down -v --remove-orphans
docker system prune -a -f
```

## Troubleshooting Guide

### Issue 1: Port Already in Use
```bash
# Check what's using the ports
netstat -ano | findstr :3000
netstat -ano | findstr :5000
netstat -ano | findstr :5432

# Kill process using port (replace PID)
taskkill /PID <PID> /F
```

### Issue 2: Database Connection Failed
```bash
# Check database logs
docker-compose logs db

# Restart database
docker-compose restart db

# Wait for database to be ready
docker-compose exec db pg_isready -U postgres
```

### Issue 3: Frontend Build Errors
```bash
# Clear Docker cache
docker system prune -f

# Rebuild frontend
docker-compose build --no-cache frontend

# Check frontend logs
docker-compose logs frontend
```

### Issue 4: Backend API Errors
```bash
# Check backend logs
docker-compose logs backend

# Verify environment variables
docker-compose exec backend env | grep DB_

# Test database connection
docker-compose exec backend node -e "console.log('Testing DB connection...')"
```

### Issue 5: Complete Reset
```bash
# Stop everything
docker-compose down -v --remove-orphans

# Remove all Docker data
docker system prune -a -f
docker volume prune -f

# Rebuild from scratch
docker-compose up --build
```

## Production Considerations

### Security Updates
1. Change JWT_SECRET to a secure random string
2. Update database passwords
3. Configure proper CORS origins
4. Enable HTTPS
5. Set up proper logging

### Performance Optimization
1. Use multi-stage builds (already implemented)
2. Enable gzip compression (configured in nginx)
3. Set up CDN for static assets
4. Configure database connection pooling
5. Implement caching strategies

### Monitoring
1. Set up health checks (already implemented)
2. Configure log aggregation
3. Monitor resource usage
4. Set up alerts for service failures

## Environment Variables

### Backend (.env)
```env
NODE_ENV=production
PORT=5000
DB_HOST=db
DB_PORT=5432
DB_NAME=talkhub
DB_USER=postgres
DB_PASSWORD=postgres
JWT_SECRET=your_super_secret_jwt_key_change_this_in_production
JWT_EXPIRES_IN=7d
CORS_ORIGIN=http://localhost:3000
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
LOG_LEVEL=info
```

### Frontend (.env)
```env
REACT_APP_API_URL=http://localhost:5000/api
```

## Success Indicators

✅ All three containers are running
✅ Frontend loads at http://localhost:3000
✅ Backend health check returns 200 at http://localhost:5000/api/health
✅ Database accepts connections
✅ User registration works
✅ User login works
✅ Communities can be created
✅ Posts can be created and voted on

## Next Steps

1. **Test the Application**: Create accounts, communities, and posts
2. **Customize**: Modify the code and see changes reflected
3. **Deploy**: Use this setup as a base for production deployment
4. **Scale**: Add load balancers, multiple backend instances, etc.

## Support

If you encounter issues:
1. Check the troubleshooting section above
2. Review Docker logs for error messages
3. Ensure all prerequisites are met
4. Try the complete reset procedure
