const { User, Post, Subreddit, Comment, Vote } = require('../models');

const getAllUsers = async (req, res) => {
  try {
    const users = await User.find()
      .select('-password')
      .sort({ createdAt: -1 });

    res.json({
      users,
      total: users.length
    });
  } catch (error) {
    console.error('Get all users error:', error);
    res.status(500).json({
      error: 'Failed to fetch users',
      details: error.message
    });
  }
};

const getAllPostsAdmin = async (req, res) => {
  try {
    const posts = await Post.find()
      .populate('userId', 'username email')
      .populate('subredditId', 'name')
      .sort({ createdAt: -1 });

    res.json({
      posts,
      total: posts.length
    });
  } catch (error) {
    console.error('Get all posts admin error:', error);
    res.status(500).json({
      error: 'Failed to fetch posts',
      details: error.message
    });
  }
};

const getAllSubredditsAdmin = async (req, res) => {
  try {
    const subreddits = await Subreddit.find()
      .sort({ createdAt: -1 });

    // Get post counts for each subreddit
    const subredditsWithCounts = await Promise.all(
      subreddits.map(async (subreddit) => {
        const postCount = await Post.countDocuments({ subredditId: subreddit._id });
        return {
          ...subreddit.toObject(),
          postCount
        };
      })
    );

    res.json({
      subreddits: subredditsWithCounts,
      total: subreddits.length
    });
  } catch (error) {
    console.error('Get all subreddits admin error:', error);
    res.status(500).json({
      error: 'Failed to fetch subreddits',
      details: error.message
    });
  }
};

const deletePost = async (req, res) => {
  try {
    const { id } = req.params;

    const post = await Post.findById(id);
    if (!post) {
      return res.status(404).json({ error: 'Post not found' });
    }

    // Delete associated comments and votes
    await Comment.deleteMany({ postId: id });
    await Vote.deleteMany({ postId: id });
    
    // Delete the post
    await Post.findByIdAndDelete(id);

    res.json({
      message: 'Post and associated data deleted successfully'
    });
  } catch (error) {
    console.error('Delete post error:', error);
    res.status(500).json({
      error: 'Failed to delete post',
      details: error.message
    });
  }
};

const deleteSubreddit = async (req, res) => {
  try {
    const { id } = req.params;

    const subreddit = await Subreddit.findById(id);
    if (!subreddit) {
      return res.status(404).json({ error: 'Subreddit not found' });
    }

    // Get all posts in this subreddit
    const posts = await Post.find({ subredditId: id });
    const postIds = posts.map(post => post._id);

    // Delete all comments and votes for posts in this subreddit
    await Comment.deleteMany({ postId: { $in: postIds } });
    await Vote.deleteMany({ postId: { $in: postIds } });
    
    // Delete all posts in this subreddit
    await Post.deleteMany({ subredditId: id });
    
    // Delete the subreddit
    await Subreddit.findByIdAndDelete(id);

    res.json({
      message: 'Subreddit and all associated data deleted successfully'
    });
  } catch (error) {
    console.error('Delete subreddit error:', error);
    res.status(500).json({
      error: 'Failed to delete subreddit',
      details: error.message
    });
  }
};

const deleteUser = async (req, res) => {
  try {
    const { id } = req.params;

    const user = await User.findById(id);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Prevent deleting admin users
    if (user.role === 'admin') {
      return res.status(403).json({ error: 'Cannot delete admin users' });
    }

    // Get all posts by this user
    const posts = await Post.find({ userId: id });
    const postIds = posts.map(post => post._id);

    // Delete all comments and votes for posts by this user
    await Comment.deleteMany({ postId: { $in: postIds } });
    await Vote.deleteMany({ postId: { $in: postIds } });
    
    // Delete all comments by this user
    await Comment.deleteMany({ userId: id });
    
    // Delete all votes by this user
    await Vote.deleteMany({ userId: id });
    
    // Delete all posts by this user
    await Post.deleteMany({ userId: id });
    
    // Delete the user
    await User.findByIdAndDelete(id);

    res.json({
      message: 'User and all associated data deleted successfully'
    });
  } catch (error) {
    console.error('Delete user error:', error);
    res.status(500).json({
      error: 'Failed to delete user',
      details: error.message
    });
  }
};

module.exports = {
  getAllUsers,
  getAllPostsAdmin,
  getAllSubredditsAdmin,
  deletePost,
  deleteSubreddit,
  deleteUser
};
