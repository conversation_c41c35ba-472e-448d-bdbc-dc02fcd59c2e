const auth = require('./auth');

const adminAuth = async (req, res, next) => {
  // First check if user is authenticated
  await auth(req, res, (err) => {
    if (err) return next(err);
    
    // Then check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({ 
        error: 'Access denied. Admin privileges required.' 
      });
    }
    
    next();
  });
};

module.exports = adminAuth;
