import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useAuth } from '../contexts/AuthContext';
import { Con<PERSON>er, Card, Button, Input, ErrorMessage, LoadingSpinner, theme } from '../styles/GlobalStyles';

const ProfileContainer = styled(Container)`
  padding-top: ${theme.spacing.xl};
  padding-bottom: ${theme.spacing.xl};
  max-width: 800px;
`;

const ProfileHeader = styled.div`
  text-align: center;
  margin-bottom: ${theme.spacing.xl};
`;

const Avatar = styled.div`
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: linear-gradient(135deg, ${theme.colors.primary}, ${theme.colors.secondary});
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto ${theme.spacing.lg};
  font-size: 3rem;
  font-weight: bold;
  color: white;
  text-transform: uppercase;
`;

const Username = styled.h1`
  color: ${theme.colors.textPrimary};
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: ${theme.spacing.sm};
`;

const UserInfo = styled.div`
  color: ${theme.colors.textSecondary};
  font-size: 1rem;
`;

const ProfileSection = styled.div`
  margin-bottom: ${theme.spacing.xl};
`;

const SectionTitle = styled.h2`
  color: ${theme.colors.textPrimary};
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: ${theme.spacing.lg};
  padding-bottom: ${theme.spacing.sm};
  border-bottom: 2px solid ${theme.colors.border};
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing.lg};
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing.xs};
`;

const Label = styled.label`
  font-weight: 500;
  color: ${theme.colors.textPrimary};
  font-size: 0.875rem;
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: ${theme.spacing.lg};
  margin-bottom: ${theme.spacing.xl};
`;

const StatCard = styled(Card)`
  text-align: center;
  padding: ${theme.spacing.lg};
`;

const StatNumber = styled.div`
  font-size: 2rem;
  font-weight: bold;
  color: ${theme.colors.primary};
  margin-bottom: ${theme.spacing.xs};
`;

const StatLabel = styled.div`
  color: ${theme.colors.textSecondary};
  font-size: 0.875rem;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: ${theme.spacing.md};
  justify-content: flex-end;
  margin-top: ${theme.spacing.lg};

  @media (max-width: ${theme.breakpoints.sm}) {
    flex-direction: column;
  }
`;

const Profile: React.FC = () => {
  const { state } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    username: state.user?.username || '',
    email: state.user?.email || '',
  });

  useEffect(() => {
    if (state.user) {
      setFormData({
        username: state.user.username,
        email: state.user.email,
      });
    }
  }, [state.user]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));

    // Clear messages when user starts typing
    if (error) setError(null);
    if (success) setSuccess(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Simulate API call for now
      await new Promise(resolve => setTimeout(resolve, 1000));
      setSuccess('Profile updated successfully!');
      setIsEditing(false);
    } catch (err: any) {
      setError('Failed to update profile. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    setError(null);
    setSuccess(null);
    if (state.user) {
      setFormData({
        username: state.user.username,
        email: state.user.email,
      });
    }
  };

  if (!state.user) {
    return (
      <ProfileContainer>
        <Card>
          <div style={{ textAlign: 'center', padding: theme.spacing.xl }}>
            <LoadingSpinner />
            <div style={{ marginTop: theme.spacing.md }}>Loading profile...</div>
          </div>
        </Card>
      </ProfileContainer>
    );
  }

  const userInitials = state.user.username.substring(0, 2);

  return (
    <ProfileContainer>
      <Card>
        <ProfileHeader>
          <Avatar>{userInitials}</Avatar>
          <Username>{state.user.username}</Username>
          <UserInfo>
            Member since {new Date(state.user.createdAt || Date.now()).toLocaleDateString()}
            {state.user.role === 'admin' && (
              <div style={{ marginTop: theme.spacing.xs }}>
                <span style={{ 
                  background: theme.colors.primary, 
                  color: 'white', 
                  padding: '2px 8px', 
                  borderRadius: theme.borderRadius.sm,
                  fontSize: '0.75rem',
                  fontWeight: 'bold'
                }}>
                  ADMIN
                </span>
              </div>
            )}
          </UserInfo>
        </ProfileHeader>

        <StatsGrid>
          <StatCard>
            <StatNumber>0</StatNumber>
            <StatLabel>Posts Created</StatLabel>
          </StatCard>
          <StatCard>
            <StatNumber>0</StatNumber>
            <StatLabel>Comments Made</StatLabel>
          </StatCard>
          <StatCard>
            <StatNumber>0</StatNumber>
            <StatLabel>Communities Joined</StatLabel>
          </StatCard>
        </StatsGrid>

        <ProfileSection>
          <SectionTitle>Profile Information</SectionTitle>
          
          {isEditing ? (
            <Form onSubmit={handleSubmit}>
              <FormGroup>
                <Label htmlFor="username">Username</Label>
                <Input
                  type="text"
                  id="username"
                  name="username"
                  value={formData.username}
                  onChange={handleInputChange}
                  disabled={isLoading}
                />
              </FormGroup>

              <FormGroup>
                <Label htmlFor="email">Email</Label>
                <Input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  disabled={isLoading}
                />
              </FormGroup>

              {error && <ErrorMessage>{error}</ErrorMessage>}
              {success && (
                <div style={{ 
                  color: theme.colors.success, 
                  fontSize: '0.875rem',
                  padding: theme.spacing.sm,
                  background: `${theme.colors.success}10`,
                  borderRadius: theme.borderRadius.sm,
                  border: `1px solid ${theme.colors.success}30`
                }}>
                  {success}
                </div>
              )}

              <ButtonGroup>
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleCancel}
                  disabled={isLoading}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isLoading}
                >
                  {isLoading ? <LoadingSpinner /> : 'Save Changes'}
                </Button>
              </ButtonGroup>
            </Form>
          ) : (
            <div>
              <FormGroup>
                <Label>Username</Label>
                <div style={{ 
                  padding: theme.spacing.sm,
                  background: theme.colors.surfaceBackground,
                  borderRadius: theme.borderRadius.md,
                  color: theme.colors.textPrimary
                }}>
                  {state.user.username}
                </div>
              </FormGroup>

              <FormGroup>
                <Label>Email</Label>
                <div style={{ 
                  padding: theme.spacing.sm,
                  background: theme.colors.surfaceBackground,
                  borderRadius: theme.borderRadius.md,
                  color: theme.colors.textPrimary
                }}>
                  {state.user.email}
                </div>
              </FormGroup>

              <ButtonGroup>
                <Button
                  type="button"
                  onClick={() => setIsEditing(true)}
                >
                  Edit Profile
                </Button>
              </ButtonGroup>
            </div>
          )}
        </ProfileSection>
      </Card>
    </ProfileContainer>
  );
};

export default Profile;