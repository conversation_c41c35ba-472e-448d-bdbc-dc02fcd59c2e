const { Post, User, Subreddit, Comment, Vote } = require('../models');

// Helper function to get vote counts for a post
const getVoteCounts = async (postId) => {
  const votes = await Vote.find({ postId });
  
  const upvotes = votes.filter(vote => vote.value === 1).length;
  const downvotes = votes.filter(vote => vote.value === -1).length;
  const score = upvotes - downvotes;

  return {
    score,
    upvotes,
    downvotes,
    totalVotes: votes.length
  };
};

const getAllPosts = async (req, res) => {
  try {
    const posts = await Post.find()
      .populate('userId', 'username')
      .populate('subredditId', 'name')
      .sort({ createdAt: -1 });

    res.json({
      posts
    });
  } catch (error) {
    console.error('Get all posts error:', error);
    res.status(500).json({
      error: 'Failed to fetch posts',
      details: error.message
    });
  }
};

const createPost = async (req, res) => {
  try {
    const { title, body, subredditId } = req.body;
    const userId = req.user.id;

    // Check if subreddit exists
    const subreddit = await Subreddit.findById(subredditId);
    if (!subreddit) {
      return res.status(404).json({ error: 'Subreddit not found' });
    }

    const post = new Post({
      title,
      body,
      userId,
      subredditId
    });

    await post.save();

    // Populate the created post with associations
    const createdPost = await Post.findById(post._id)
      .populate('userId', 'username')
      .populate('subredditId', 'name');

    res.status(201).json({
      message: 'Post created successfully',
      post: createdPost
    });
  } catch (error) {
    console.error('Create post error:', error);
    res.status(500).json({
      error: 'Failed to create post',
      details: error.message
    });
  }
};

const getPostComments = async (req, res) => {
  try {
    const { id } = req.params;

    const post = await Post.findById(id)
      .populate('userId', 'username')
      .populate('subredditId', 'name');

    if (!post) {
      return res.status(404).json({ error: 'Post not found' });
    }

    // Get all comments for the post
    const comments = await Comment.find({ postId: id })
      .populate('userId', 'username')
      .sort({ createdAt: 1 });

    // Build comment tree structure
    const commentMap = new Map();
    const rootComments = [];

    // First pass: create map of all comments
    comments.forEach(comment => {
      commentMap.set(comment._id.toString(), {
        ...comment.toObject(),
        replies: []
      });
    });

    // Second pass: build tree structure
    comments.forEach(comment => {
      const commentData = commentMap.get(comment._id.toString());
      if (comment.parentCommentId) {
        const parent = commentMap.get(comment.parentCommentId.toString());
        if (parent) {
          parent.replies.push(commentData);
        }
      } else {
        rootComments.push(commentData);
      }
    });

    // Get vote counts for the post
    const voteCounts = await getVoteCounts(id);

    res.json({
      post: {
        ...post.toObject(),
        ...voteCounts
      },
      comments: rootComments
    });
  } catch (error) {
    console.error('Get post comments error:', error);
    res.status(500).json({
      error: 'Failed to fetch post comments',
      details: error.message
    });
  }
};

const voteOnPost = async (req, res) => {
  try {
    const { id } = req.params;
    const { value } = req.body; // 1 for upvote, -1 for downvote
    const userId = req.user.id;

    if (![1, -1].includes(value)) {
      return res.status(400).json({ error: 'Vote value must be 1 or -1' });
    }

    const post = await Post.findById(id);
    if (!post) {
      return res.status(404).json({ error: 'Post not found' });
    }

    // Check if user already voted on this post
    const existingVote = await Vote.findOne({ userId, postId: id });

    if (existingVote) {
      if (existingVote.value === value) {
        // Remove vote if same value
        await Vote.findByIdAndDelete(existingVote._id);
        return res.json({ message: 'Vote removed' });
      } else {
        // Update vote if different value
        existingVote.value = value;
        await existingVote.save();
        return res.json({ message: 'Vote updated', vote: existingVote });
      }
    } else {
      // Create new vote
      const vote = new Vote({
        userId,
        postId: id,
        value
      });
      await vote.save();
      return res.json({ message: 'Vote created', vote });
    }
  } catch (error) {
    console.error('Vote on post error:', error);
    res.status(500).json({
      error: 'Failed to vote on post',
      details: error.message
    });
  }
};

module.exports = {
  getAllPosts,
  createPost,
  getPostComments,
  voteOnPost
};