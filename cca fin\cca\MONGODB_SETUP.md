# TalkHub MongoDB Setup Guide

This guide explains how to set up and run TalkHub with MongoDB instead of SQLite3.

## Prerequisites

- Docker and Docker Compose installed
- Node.js 18+ (for local development)
- MongoDB (for local development without Docker)

## Docker Setup (Recommended)

### 1. Build and Run with Docker Compose

```bash
# Build and start all services (MongoDB, Backend, Frontend)
docker-compose up --build

# Or run in detached mode
docker-compose up --build -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down

# Stop and remove volumes (clears database)
docker-compose down -v
```

### 2. MongoDB Configuration

The Docker setup includes:
- MongoDB 7.0 with authentication
- Database: `talkhub`
- Username: `admin`
- Password: `password123`
- Port: `27017`

### 3. Environment Variables

The following environment variables are configured in docker-compose.yml:

```env
MONGODB_URI=******************************************************************
NODE_ENV=development
PORT=5000
JWT_SECRET=your_super_secret_jwt_key_change_this_in_production_minimum_32_characters
```

## Local Development Setup

### 1. Install MongoDB Locally

**Windows:**
```bash
# Using Chocolatey
choco install mongodb

# Or download from https://www.mongodb.com/try/download/community
```

**macOS:**
```bash
# Using Homebrew
brew tap mongodb/brew
brew install mongodb-community
```

**Linux (Ubuntu/Debian):**
```bash
# Import MongoDB public GPG key
wget -qO - https://www.mongodb.org/static/pgp/server-7.0.asc | sudo apt-key add -

# Add MongoDB repository
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu jammy/mongodb-org/7.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-7.0.list

# Install MongoDB
sudo apt-get update
sudo apt-get install -y mongodb-org
```

### 2. Start MongoDB Service

**Windows:**
```bash
# Start MongoDB service
net start MongoDB

# Or run manually
mongod --dbpath C:\data\db
```

**macOS/Linux:**
```bash
# Start MongoDB service
sudo systemctl start mongod

# Or run manually
mongod --dbpath /data/db
```

### 3. Install Dependencies and Run

```bash
# Navigate to backend directory
cd backend

# Install dependencies
npm install

# Copy environment file
cp .env.example .env

# Edit .env file to set MongoDB URI (default: mongodb://localhost:27017/talkhub)

# Start the backend server
npm run dev

# In another terminal, start the frontend
cd ../frontend
npm install
npm start
```

## Database Schema

The MongoDB database includes the following collections:

### Users
```javascript
{
  _id: ObjectId,
  username: String (unique, 3-30 chars, alphanumeric),
  email: String (unique, valid email format),
  password: String (hashed, 6-100 chars),
  createdAt: Date,
  updatedAt: Date
}
```

### Subreddits
```javascript
{
  _id: ObjectId,
  name: String (unique, 3-50 chars, alphanumeric + underscore),
  description: String (max 500 chars),
  createdAt: Date,
  updatedAt: Date
}
```

### Posts
```javascript
{
  _id: ObjectId,
  title: String (5-300 chars),
  body: String,
  userId: ObjectId (ref: User),
  subredditId: ObjectId (ref: Subreddit),
  createdAt: Date,
  updatedAt: Date
}
```

### Comments
```javascript
{
  _id: ObjectId,
  content: String (1-10000 chars),
  postId: ObjectId (ref: Post),
  userId: ObjectId (ref: User),
  parentCommentId: ObjectId (ref: Comment, nullable),
  createdAt: Date,
  updatedAt: Date
}
```

### Votes
```javascript
{
  _id: ObjectId,
  userId: ObjectId (ref: User),
  postId: ObjectId (ref: Post),
  value: Number (-1 or 1),
  createdAt: Date,
  updatedAt: Date
}
```

## API Endpoints

All API endpoints remain the same as before:

- `POST /api/register` - User registration
- `POST /api/login` - User login
- `GET /api/subreddits` - Get all subreddits
- `POST /api/subreddits` - Create subreddit
- `GET /api/subreddits/:name/posts` - Get subreddit posts
- `POST /api/posts` - Create post
- `GET /api/posts/:id/comments` - Get post with comments
- `POST /api/posts/:id/vote` - Vote on post
- `POST /api/comments` - Create comment

## Troubleshooting

### Common Issues

1. **MongoDB Connection Error**
   - Ensure MongoDB is running
   - Check the connection string in .env file
   - Verify network connectivity (for Docker)

2. **Authentication Error**
   - Check MongoDB credentials
   - Ensure the database exists
   - Verify user permissions

3. **Docker Build Issues**
   - Clear Docker cache: `docker system prune -a`
   - Rebuild without cache: `docker-compose build --no-cache`

4. **Port Conflicts**
   - Check if ports 3000, 5000, or 27017 are in use
   - Modify ports in docker-compose.yml if needed

### Useful Commands

```bash
# Connect to MongoDB container
docker exec -it talkhub-mongodb mongosh -u admin -p password123

# View MongoDB logs
docker logs talkhub-mongodb

# Reset database (removes all data)
docker-compose down -v
docker-compose up --build

# Backup database
docker exec talkhub-mongodb mongodump --uri="********************************************************************" --out=/backup

# Restore database
docker exec talkhub-mongodb mongorestore --uri="********************************************************************" /backup/talkhub
```

## Migration from SQLite3

The migration from SQLite3 to MongoDB has been completed with the following changes:

1. **Dependencies**: Replaced `sequelize`, `sqlite3`, and `pg` with `mongoose`
2. **Models**: Converted Sequelize models to Mongoose schemas
3. **Controllers**: Updated to use MongoDB queries instead of Sequelize
4. **Configuration**: New MongoDB connection configuration
5. **Docker**: Added MongoDB service to docker-compose.yml

All existing functionality is preserved, including:
- User authentication and authorization
- Subreddit management
- Post creation and voting
- Threaded comments
- API endpoints and responses

The application now uses MongoDB's ObjectId instead of auto-incrementing integers for primary keys.