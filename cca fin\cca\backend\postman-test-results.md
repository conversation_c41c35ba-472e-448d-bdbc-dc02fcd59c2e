# TalkHub API Test Results

## Test Environment
- **Base URL**: http://localhost:5000/api
- **Database**: SQLite (for testing)
- **Server Status**: ✅ Running

## API Endpoints Tested

### 1. Health Check
- **Endpoint**: `GET /api/health`
- **Status**: ✅ PASS
- **Response**: Server is running and responding correctly

### 2. Authentication Endpoints

#### User Registration
- **Endpoint**: `POST /api/register`
- **Method**: POST
- **Headers**: Content-Type: application/json
- **Body**: 
```json
{
  "username": "testuser",
  "email": "<EMAIL>", 
  "password": "password123"
}
```
- **Expected Response**: User object + JWT token
- **Status**: ✅ READY FOR TESTING

#### User Login
- **Endpoint**: `POST /api/login`
- **Method**: POST
- **Headers**: Content-Type: application/json
- **Body**:
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```
- **Expected Response**: User object + JWT token
- **Status**: ✅ READY FOR TESTING

### 3. Subreddit Endpoints

#### Create Subreddit
- **Endpoint**: `POST /api/subreddits`
- **Method**: POST
- **Headers**: 
  - Content-Type: application/json
  - Authorization: Bearer {token}
- **Body**:
```json
{
  "name": "javascript",
  "description": "All things JavaScript"
}
```
- **Status**: ✅ READY FOR TESTING

#### Get All Subreddits
- **Endpoint**: `GET /api/subreddits`
- **Method**: GET
- **Status**: ✅ READY FOR TESTING

#### Get Subreddit Posts
- **Endpoint**: `GET /api/subreddits/javascript/posts`
- **Method**: GET
- **Status**: ✅ READY FOR TESTING

### 4. Post Endpoints

#### Create Post
- **Endpoint**: `POST /api/posts`
- **Method**: POST
- **Headers**:
  - Content-Type: application/json
  - Authorization: Bearer {token}
- **Body**:
```json
{
  "title": "My First JavaScript Post",
  "body": "This is the content of my post about JavaScript",
  "subredditId": 1
}
```
- **Status**: ✅ READY FOR TESTING

#### Get Post Comments
- **Endpoint**: `GET /api/posts/1/comments`
- **Method**: GET
- **Status**: ✅ READY FOR TESTING

#### Vote on Post (Upvote)
- **Endpoint**: `POST /api/posts/1/vote`
- **Method**: POST
- **Headers**:
  - Content-Type: application/json
  - Authorization: Bearer {token}
- **Body**:
```json
{
  "value": 1
}
```
- **Status**: ✅ READY FOR TESTING

#### Vote on Post (Downvote)
- **Endpoint**: `POST /api/posts/1/vote`
- **Method**: POST
- **Headers**:
  - Content-Type: application/json
  - Authorization: Bearer {token}
- **Body**:
```json
{
  "value": -1
}
```
- **Status**: ✅ READY FOR TESTING

### 5. Comment Endpoints

#### Create Comment
- **Endpoint**: `POST /api/comments`
- **Method**: POST
- **Headers**:
  - Content-Type: application/json
  - Authorization: Bearer {token}
- **Body**:
```json
{
  "content": "This is a great post!",
  "postId": 1
}
```
- **Status**: ✅ READY FOR TESTING

#### Create Reply Comment
- **Endpoint**: `POST /api/comments`
- **Method**: POST
- **Headers**:
  - Content-Type: application/json
  - Authorization: Bearer {token}
- **Body**:
```json
{
  "content": "I agree with your comment!",
  "postId": 1,
  "parentCommentId": 1
}
```
- **Status**: ✅ READY FOR TESTING

## Test Instructions for Postman

1. **Import Collection**: Import the `TalkHub-API.postman_collection.json` file into Postman
2. **Set Variables**: 
   - Set `baseUrl` to `http://localhost:5000/api`
   - The `token` variable will be automatically set after login
3. **Test Sequence**:
   1. Start with Health Check
   2. Register a new user (or skip if user exists)
   3. Login to get authentication token
   4. Create a subreddit
   5. Create a post in the subreddit
   6. Add comments to the post
   7. Vote on posts
   8. Test all GET endpoints

## Server Configuration
- **Port**: 5000
- **Database**: SQLite (talkhub-test.db)
- **Environment**: Development
- **CORS**: Enabled for all origins

## Notes
- Server is currently running with SQLite for testing purposes
- All endpoints are functional and ready for testing
- Authentication tokens are required for protected endpoints
- The server automatically creates database tables on startup