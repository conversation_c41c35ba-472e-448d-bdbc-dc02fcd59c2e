const express = require('express');
const { 
  getAllUsers, 
  getAllPostsAdmin, 
  getAllSubredditsAdmin, 
  deletePost, 
  deleteSubreddit,
  deleteUser
} = require('../controllers/adminController');
const adminAuth = require('../middleware/admin');

const router = express.Router();

// All admin routes require admin authentication
router.use(adminAuth);

// GET /api/admin/users - Get all users
router.get('/users', getAllUsers);

// GET /api/admin/posts - Get all posts with full details
router.get('/posts', getAllPostsAdmin);

// GET /api/admin/subreddits - Get all subreddits with full details
router.get('/subreddits', getAllSubredditsAdmin);

// DELETE /api/admin/posts/:id - Delete a post
router.delete('/posts/:id', deletePost);

// DELETE /api/admin/subreddits/:id - Delete a subreddit
router.delete('/subreddits/:id', deleteSubreddit);

// DELETE /api/admin/users/:id - Delete a user
router.delete('/users/:id', deleteUser);

module.exports = router;
