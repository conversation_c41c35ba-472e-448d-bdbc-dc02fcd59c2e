import React, { useState, useEffect } from 'react';
import { formatDistanceToNow } from 'date-fns';
import styled from 'styled-components';
import { Container, Card, Button, ErrorMessage, LoadingSpinner, theme } from '../styles/GlobalStyles';
import { apiService } from '../services/api';

const AdminContainer = styled(Container)`
  padding-top: ${theme.spacing.xl};
  padding-bottom: ${theme.spacing.xl};
`;

const Title = styled.h1`
  color: ${theme.colors.textPrimary};
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: ${theme.spacing.xl};
  text-align: center;
`;

const TabContainer = styled.div`
  display: flex;
  gap: ${theme.spacing.md};
  margin-bottom: ${theme.spacing.xl};
  border-bottom: 1px solid ${theme.colors.border};
`;

const Tab = styled.button<{ active: boolean }>`
  padding: ${theme.spacing.md} ${theme.spacing.lg};
  background: ${props => props.active ? theme.colors.primary : 'transparent'};
  color: ${props => props.active ? theme.colors.white : theme.colors.textPrimary};
  border: none;
  border-radius: ${theme.borderRadius.md} ${theme.borderRadius.md} 0 0;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;

  &:hover {
    background: ${props => props.active ? theme.colors.primary : theme.colors.primary}20;
  }
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: ${theme.spacing.lg};
  margin-bottom: ${theme.spacing.xl};
`;

const StatCard = styled(Card)`
  padding: ${theme.spacing.lg};
  text-align: center;
`;

const StatNumber = styled.div`
  font-size: 2rem;
  font-weight: 700;
  color: ${theme.colors.primary};
  margin-bottom: ${theme.spacing.xs};
`;

const StatLabel = styled.div`
  color: ${theme.colors.textSecondary};
  font-size: 0.875rem;
`;

const DataTable = styled.div`
  background: ${theme.colors.white};
  border-radius: ${theme.borderRadius.lg};
  overflow: hidden;
  box-shadow: ${theme.shadows.md};
`;

const TableHeader = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr auto;
  gap: ${theme.spacing.md};
  padding: ${theme.spacing.md};
  background: ${theme.colors.background};
  font-weight: 600;
  color: ${theme.colors.textPrimary};
  border-bottom: 1px solid ${theme.colors.border};
`;

const TableRow = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr auto;
  gap: ${theme.spacing.md};
  padding: ${theme.spacing.md};
  border-bottom: 1px solid ${theme.colors.border};
  align-items: center;

  &:hover {
    background: ${theme.colors.background};
  }
`;

const DeleteButton = styled(Button)`
  background: ${theme.colors.danger};
  color: ${theme.colors.white};
  padding: ${theme.spacing.xs} ${theme.spacing.sm};
  font-size: 0.75rem;

  &:hover {
    background: ${theme.colors.danger}dd;
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: ${theme.spacing.xl};
`;

type TabType = 'users' | 'posts' | 'subreddits';

const AdminPanel: React.FC = () => {
  const [activeTab, setActiveTab] = useState<TabType>('users');
  const [users, setUsers] = useState<any[]>([]);
  const [posts, setPosts] = useState<any[]>([]);
  const [subreddits, setSubreddits] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchData();
  }, [activeTab]);

  const fetchData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      switch (activeTab) {
        case 'users':
          const usersResponse = await apiService.getAllUsersAdmin();
          setUsers(usersResponse.users);
          break;
        case 'posts':
          const postsResponse = await apiService.getAllPostsAdmin();
          setPosts(postsResponse.posts);
          break;
        case 'subreddits':
          const subredditsResponse = await apiService.getAllSubredditsAdmin();
          setSubreddits(subredditsResponse.subreddits);
          break;
      }
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to fetch data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (id: string, type: TabType) => {
    if (!window.confirm(`Are you sure you want to delete this ${type.slice(0, -1)}?`)) {
      return;
    }

    try {
      switch (type) {
        case 'users':
          await apiService.deleteUserAdmin(id);
          setUsers(users.filter(user => user._id !== id));
          break;
        case 'posts':
          await apiService.deletePostAdmin(id);
          setPosts(posts.filter(post => post._id !== id));
          break;
        case 'subreddits':
          await apiService.deleteSubredditAdmin(id);
          setSubreddits(subreddits.filter(subreddit => subreddit._id !== id));
          break;
      }
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to delete item');
    }
  };

  const renderUsersTable = () => (
    <DataTable>
      <TableHeader>
        <div>Username</div>
        <div>Email</div>
        <div>Role</div>
        <div>Joined</div>
        <div>Actions</div>
      </TableHeader>
      {users.map((user) => (
        <TableRow key={user._id}>
          <div>{user.username}</div>
          <div>{user.email}</div>
          <div>{user.role}</div>
          <div>{formatDistanceToNow(new Date(user.createdAt))} ago</div>
          <div>
            {user.role !== 'admin' && (
              <DeleteButton onClick={() => handleDelete(user._id, 'users')}>
                Delete
              </DeleteButton>
            )}
          </div>
        </TableRow>
      ))}
    </DataTable>
  );

  const renderPostsTable = () => (
    <DataTable>
      <TableHeader>
        <div>Title</div>
        <div>Author</div>
        <div>Community</div>
        <div>Created</div>
        <div>Actions</div>
      </TableHeader>
      {posts.map((post) => (
        <TableRow key={post._id}>
          <div>{post.title}</div>
          <div>{post.userId?.username || 'Unknown'}</div>
          <div>r/{post.subredditId?.name || 'Unknown'}</div>
          <div>{formatDistanceToNow(new Date(post.createdAt))} ago</div>
          <div>
            <DeleteButton onClick={() => handleDelete(post._id, 'posts')}>
              Delete
            </DeleteButton>
          </div>
        </TableRow>
      ))}
    </DataTable>
  );

  const renderSubredditsTable = () => (
    <DataTable>
      <TableHeader>
        <div>Name</div>
        <div>Description</div>
        <div>Posts</div>
        <div>Created</div>
        <div>Actions</div>
      </TableHeader>
      {subreddits.map((subreddit) => (
        <TableRow key={subreddit._id}>
          <div>r/{subreddit.name}</div>
          <div>{subreddit.description || 'No description'}</div>
          <div>{subreddit.postCount || 0}</div>
          <div>{formatDistanceToNow(new Date(subreddit.createdAt))} ago</div>
          <div>
            <DeleteButton onClick={() => handleDelete(subreddit._id, 'subreddits')}>
              Delete
            </DeleteButton>
          </div>
        </TableRow>
      ))}
    </DataTable>
  );

  return (
    <AdminContainer>
      <Title>Admin Panel</Title>

      <StatsGrid>
        <StatCard>
          <StatNumber>{users.length}</StatNumber>
          <StatLabel>Total Users</StatLabel>
        </StatCard>
        <StatCard>
          <StatNumber>{posts.length}</StatNumber>
          <StatLabel>Total Posts</StatLabel>
        </StatCard>
        <StatCard>
          <StatNumber>{subreddits.length}</StatNumber>
          <StatLabel>Total Communities</StatLabel>
        </StatCard>
      </StatsGrid>

      <TabContainer>
        <Tab active={activeTab === 'users'} onClick={() => setActiveTab('users')}>
          Users
        </Tab>
        <Tab active={activeTab === 'posts'} onClick={() => setActiveTab('posts')}>
          Posts
        </Tab>
        <Tab active={activeTab === 'subreddits'} onClick={() => setActiveTab('subreddits')}>
          Communities
        </Tab>
      </TabContainer>

      {error && <ErrorMessage>{error}</ErrorMessage>}

      {isLoading ? (
        <LoadingContainer>
          <LoadingSpinner />
        </LoadingContainer>
      ) : (
        <>
          {activeTab === 'users' && renderUsersTable()}
          {activeTab === 'posts' && renderPostsTable()}
          {activeTab === 'subreddits' && renderSubredditsTable()}
        </>
      )}
    </AdminContainer>
  );
};

export default AdminPanel;
