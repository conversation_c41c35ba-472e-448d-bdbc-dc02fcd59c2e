import styled, { createGlobalStyle } from 'styled-components';

export const GlobalStyles = createGlobalStyle`
  :root {
    /* Modern light theme variables */
    --bg-primary: #f8fafc;
    --bg-secondary: #ffffff;
    --bg-tertiary: #f1f5f9;
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-muted: #64748b;
    --border-color: #e2e8f0;
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

    /* Gradient backgrounds */
    --gradient-primary: linear-gradient(135deg, #6366f1, #818cf8);
    --gradient-secondary: linear-gradient(135deg, #ec4899, #f472b6);
    --gradient-surface: linear-gradient(135deg, #f8fafc, #f1f5f9);
  }

  .dark-theme {
    /* Modern dark theme variables */
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;
    --border-color: #334155;
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.5), 0 2px 4px -1px rgba(0, 0, 0, 0.4);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5), 0 4px 6px -2px rgba(0, 0, 0, 0.4);

    /* Dark gradient backgrounds */
    --gradient-primary: linear-gradient(135deg, #4f46e5, #6366f1);
    --gradient-secondary: linear-gradient(135deg, #db2777, #ec4899);
    --gradient-surface: linear-gradient(135deg, #1e293b, #334155);
  }

  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
      sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background: var(--gradient-surface);
    color: var(--text-primary);
    line-height: 1.6;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow-x: hidden;
  }

  /* Add subtle background pattern */
  body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
      radial-gradient(circle at 25% 25%, rgba(99, 102, 241, 0.05) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(236, 72, 153, 0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
  }

  code {
    font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
      monospace;
  }

  a {
    text-decoration: none;
    color: inherit;
  }

  button {
    cursor: pointer;
    border: none;
    outline: none;
    font-family: inherit;
  }

  input, textarea {
    font-family: inherit;
    outline: none;
  }

  ul, ol {
    list-style: none;
  }

  img {
    max-width: 100%;
    height: auto;
  }

  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  /* Modern animations */
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideInFromLeft {
    from {
      opacity: 0;
      transform: translateX(-30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes slideInFromRight {
    from {
      opacity: 0;
      transform: translateX(30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }

  @keyframes shimmer {
    0% {
      background-position: -200px 0;
    }
    100% {
      background-position: calc(200px + 100%) 0;
    }
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  /* Utility classes for animations */
  .animate-fade-in {
    animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .animate-slide-in-left {
    animation: slideInFromLeft 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .animate-slide-in-right {
    animation: slideInFromRight 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .animate-scale-in {
    animation: scaleIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: var(--bg-tertiary);
  }

  ::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #6366f1, #818cf8);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #4f46e5, #6366f1);
  }
`;

// Theme colors
export const theme = {
  colors: {
    // Modern gradient-based primary colors
    primary: '#6366f1',
    primaryHover: '#4f46e5',
    primaryLight: '#818cf8',
    primaryDark: '#3730a3',

    secondary: '#ec4899',
    secondaryHover: '#db2777',
    secondaryLight: '#f472b6',
    secondaryDark: '#be185d',

    // Accent colors
    accent: '#06b6d4',
    accentHover: '#0891b2',
    accentLight: '#22d3ee',

    // Status colors with modern tones
    success: '#059669',
    successHover: '#047857',
    successLight: '#10b981',

    danger: '#dc2626',
    dangerHover: '#b91c1c',
    dangerLight: '#ef4444',

    warning: '#d97706',
    warningHover: '#b45309',
    warningLight: '#f59e0b',

    info: '#0284c7',
    infoHover: '#0369a1',
    infoLight: '#0ea5e9',

    // Background colors
    white: '#ffffff',
    light: '#f8fafc',
    dark: '#0f172a',

    // Enhanced gray scale
    gray50: '#f8fafc',
    gray100: '#f1f5f9',
    gray200: '#e2e8f0',
    gray300: '#cbd5e1',
    gray400: '#94a3b8',
    gray500: '#64748b',
    gray600: '#475569',
    gray700: '#334155',
    gray800: '#1e293b',
    gray900: '#0f172a',

    // Dynamic colors using CSS variables with modern defaults
    border: 'var(--border-color, #e2e8f0)',
    background: 'var(--bg-primary, #f8fafc)',
    cardBackground: 'var(--bg-secondary, #ffffff)',
    surfaceBackground: 'var(--bg-tertiary, #f1f5f9)',
    textPrimary: 'var(--text-primary, #0f172a)',
    textSecondary: 'var(--text-secondary, #475569)',
    textMuted: 'var(--text-muted, #64748b)',
  },
  spacing: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
    xxl: '3rem',
  },
  borderRadius: {
    sm: '0.25rem',
    md: '0.375rem',
    lg: '0.5rem',
    xl: '0.75rem',
    full: '9999px',
  },
  shadows: {
    xs: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    sm: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
    '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
    inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
    glow: '0 0 20px rgba(99, 102, 241, 0.3)',
    glowHover: '0 0 30px rgba(99, 102, 241, 0.4)',
  },
  breakpoints: {
    xs: '475px',
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px',
  },
  transitions: {
    fast: '0.15s cubic-bezier(0.4, 0, 0.2, 1)',
    normal: '0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    slow: '0.5s cubic-bezier(0.4, 0, 0.2, 1)',
    bounce: '0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55)',
    spring: '0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
  },
};

// Common styled components
export const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 ${theme.spacing.md};

  @media (min-width: ${theme.breakpoints.sm}) {
    padding: 0 ${theme.spacing.lg};
  }
`;

export const Card = styled.div`
  background: ${theme.colors.cardBackground};
  border-radius: ${theme.borderRadius.lg};
  box-shadow: ${theme.shadows.sm};
  padding: ${theme.spacing.lg};
  margin-bottom: ${theme.spacing.md};
  border: 1px solid ${theme.colors.border};
  transition: all ${theme.transitions.normal};
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, ${theme.colors.primary}, ${theme.colors.secondary});
    opacity: 0;
    transition: opacity ${theme.transitions.fast};
  }

  &:hover {
    box-shadow: ${theme.shadows.lg};
    transform: translateY(-2px);
    border-color: ${theme.colors.primaryLight};

    &::before {
      opacity: 1;
    }
  }
`;

export const Button = styled.button<{
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  $fullWidth?: boolean;
}>`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  border-radius: ${theme.borderRadius.md};
  transition: all ${theme.transitions.normal};
  text-decoration: none;
  cursor: pointer;
  border: 1px solid transparent;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left ${theme.transitions.normal};
  }

  &:hover::before {
    left: 100%;
  }

  &:active {
    transform: translateY(1px);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;

    &:hover::before {
      left: -100%;
    }
  }

  ${({ size = 'md' }) => {
    switch (size) {
      case 'sm':
        return `
          padding: ${theme.spacing.xs} ${theme.spacing.sm};
          font-size: 0.875rem;
        `;
      case 'lg':
        return `
          padding: ${theme.spacing.md} ${theme.spacing.xl};
          font-size: 1.125rem;
        `;
      default:
        return `
          padding: ${theme.spacing.sm} ${theme.spacing.md};
          font-size: 1rem;
        `;
    }
  }}

  ${({ variant = 'primary' }) => {
    switch (variant) {
      case 'secondary':
        return `
          background: linear-gradient(135deg, ${theme.colors.secondary}, ${theme.colors.secondaryLight});
          color: ${theme.colors.white};
          box-shadow: ${theme.shadows.sm};
          &:hover {
            background: linear-gradient(135deg, ${theme.colors.secondaryHover}, ${theme.colors.secondary});
            box-shadow: ${theme.shadows.md};
            transform: translateY(-2px);
          }
        `;
      case 'outline':
        return `
          background-color: transparent;
          color: ${theme.colors.primary};
          border-color: ${theme.colors.primary};
          &:hover {
            background: linear-gradient(135deg, ${theme.colors.primary}, ${theme.colors.primaryLight});
            color: ${theme.colors.white};
            box-shadow: ${theme.shadows.md};
            transform: translateY(-2px);
          }
        `;
      case 'ghost':
        return `
          background-color: transparent;
          color: ${theme.colors.textSecondary};
          &:hover {
            background-color: ${theme.colors.gray100};
            color: ${theme.colors.textPrimary};
            transform: translateY(-1px);
          }
        `;
      default:
        return `
          background: linear-gradient(135deg, ${theme.colors.primary}, ${theme.colors.primaryLight});
          color: ${theme.colors.white};
          box-shadow: ${theme.shadows.sm};
          &:hover {
            background: linear-gradient(135deg, ${theme.colors.primaryHover}, ${theme.colors.primary});
            box-shadow: ${theme.shadows.md};
            transform: translateY(-2px);
          }
        `;
    }
  }}

  ${({ $fullWidth }) =>
    $fullWidth &&
    `
    width: 100%;
  `}

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    &:hover {
      background-color: ${theme.colors.primary};
    }
  }
`;

export const Input = styled.input`
  width: 100%;
  padding: ${theme.spacing.md} ${theme.spacing.lg};
  border: 2px solid ${theme.colors.border};
  border-radius: ${theme.borderRadius.lg};
  font-size: 1rem;
  background: ${theme.colors.white};
  color: ${theme.colors.textPrimary};
  transition: all ${theme.transitions.normal};
  position: relative;

  &:focus {
    border-color: ${theme.colors.primary};
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    outline: none;
    transform: translateY(-1px);
  }

  &:hover {
    border-color: ${theme.colors.primaryLight};
  }

  &::placeholder {
    color: ${theme.colors.textMuted};
    transition: color ${theme.transitions.fast};
  }

  &:focus::placeholder {
    color: ${theme.colors.textSecondary};
  }

  &:disabled {
    background: ${theme.colors.gray100};
    cursor: not-allowed;
    opacity: 0.7;
  }
`;

export const TextArea = styled.textarea`
  width: 100%;
  padding: ${theme.spacing.md} ${theme.spacing.lg};
  border: 2px solid ${theme.colors.border};
  border-radius: ${theme.borderRadius.lg};
  font-size: 1rem;
  background: ${theme.colors.white};
  color: ${theme.colors.textPrimary};
  transition: all ${theme.transitions.normal};
  resize: vertical;
  min-height: 120px;
  font-family: inherit;

  &:focus {
    border-color: ${theme.colors.primary};
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    outline: none;
    transform: translateY(-1px);
  }

  &:hover {
    border-color: ${theme.colors.primaryLight};
  }

  &::placeholder {
    color: ${theme.colors.textMuted};
    transition: color ${theme.transitions.fast};
  }

  &:focus::placeholder {
    color: ${theme.colors.textSecondary};
  }

  &:disabled {
    background: ${theme.colors.gray100};
    cursor: not-allowed;
    opacity: 0.7;
  }

  &::placeholder {
    color: ${theme.colors.textMuted};
  }
`;

export const ErrorMessage = styled.div`
  color: ${theme.colors.danger};
  background: linear-gradient(135deg, ${theme.colors.dangerLight}10, ${theme.colors.danger}05);
  border: 1px solid ${theme.colors.dangerLight}30;
  border-radius: ${theme.borderRadius.md};
  padding: ${theme.spacing.sm} ${theme.spacing.md};
  font-size: 0.875rem;
  margin-top: ${theme.spacing.xs};
  display: flex;
  align-items: center;
  gap: ${theme.spacing.xs};
  animation: fadeIn 0.3s ease-out;

  &::before {
    content: '⚠️';
    font-size: 1rem;
  }
`;

export const LoadingSpinner = styled.div`
  width: 24px;
  height: 24px;
  border: 3px solid transparent;
  border-top: 3px solid ${theme.colors.primary};
  border-right: 3px solid ${theme.colors.primaryLight};
  border-radius: 50%;
  animation: modernSpin 1s cubic-bezier(0.68, -0.55, 0.265, 1.55) infinite;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    border: 3px solid transparent;
    border-bottom: 3px solid ${theme.colors.secondary};
    border-left: 3px solid ${theme.colors.secondaryLight};
    border-radius: 50%;
    animation: modernSpin 1.5s cubic-bezier(0.68, -0.55, 0.265, 1.55) infinite reverse;
  }

  @keyframes modernSpin {
    0% {
      transform: rotate(0deg);
      filter: hue-rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
      filter: hue-rotate(360deg);
    }
  }
`;
