{"info": {"name": "TalkHub API", "description": "Reddit-style backend API collection", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:5000/api", "type": "string"}, {"key": "token", "value": "", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"testuser\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{baseUrl}}/register", "host": ["{{baseUrl}}"], "path": ["register"]}}}, {"name": "Login User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{baseUrl}}/login", "host": ["{{baseUrl}}"], "path": ["login"]}}}]}, {"name": "Subreddits", "item": [{"name": "Create Subreddit", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"javascript\",\n  \"description\": \"All things JavaScript\"\n}"}, "url": {"raw": "{{baseUrl}}/subreddits", "host": ["{{baseUrl}}"], "path": ["subreddits"]}}}, {"name": "Get All Subreddits", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/subreddits", "host": ["{{baseUrl}}"], "path": ["subreddits"]}}}, {"name": "Get Subreddit Posts", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/subreddits/javascript/posts", "host": ["{{baseUrl}}"], "path": ["subreddits", "javascript", "posts"]}}}]}, {"name": "Posts", "item": [{"name": "Create Post", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"My First JavaScript Post\",\n  \"body\": \"This is the content of my post about JavaScript\",\n  \"subredditId\": 1\n}"}, "url": {"raw": "{{baseUrl}}/posts", "host": ["{{baseUrl}}"], "path": ["posts"]}}}, {"name": "Get Post Comments", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/posts/1/comments", "host": ["{{baseUrl}}"], "path": ["posts", "1", "comments"]}}}, {"name": "Vote on Post (Upvote)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"value\": 1\n}"}, "url": {"raw": "{{baseUrl}}/posts/1/vote", "host": ["{{baseUrl}}"], "path": ["posts", "1", "vote"]}}}, {"name": "Vote on Post (Downvote)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"value\": -1\n}"}, "url": {"raw": "{{baseUrl}}/posts/1/vote", "host": ["{{baseUrl}}"], "path": ["posts", "1", "vote"]}}}]}, {"name": "Comments", "item": [{"name": "Create Comment", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"content\": \"This is a great post!\",\n  \"postId\": 1\n}"}, "url": {"raw": "{{baseUrl}}/comments", "host": ["{{baseUrl}}"], "path": ["comments"]}}}, {"name": "Create Rep<PERSON> Comment", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"content\": \"I agree with your comment!\",\n  \"postId\": 1,\n  \"parentCommentId\": 1\n}"}, "url": {"raw": "{{baseUrl}}/comments", "host": ["{{baseUrl}}"], "path": ["comments"]}}}]}, {"name": "Health Check", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/health", "host": ["{{baseUrl}}"], "path": ["health"]}}}]}