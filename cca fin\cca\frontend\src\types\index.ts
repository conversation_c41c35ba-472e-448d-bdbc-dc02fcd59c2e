// User types
export interface User {
  id: string;
  username: string;
  email: string;
  role?: string;
  createdAt: string;
  updatedAt: string;
}

export interface AuthResponse {
  message: string;
  token: string;
  user: User;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
}

// Subreddit types
export interface Subreddit {
  id: string;
  name: string;
  description: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateSubredditRequest {
  name: string;
  description: string;
}

// Post types
export interface Post {
  id: string;
  title: string;
  body?: string;
  userId: string;
  subredditId: string;
  createdAt: string;
  updatedAt: string;
  author: {
    id: string;
    username: string;
  };
  subreddit: {
    id: string;
    name: string;
  };
  score?: number;
  upvotes?: number;
  downvotes?: number;
  totalVotes?: number;
  userVote?: 1 | -1 | null;
  commentsCount?: number;
}

export interface CreatePostRequest {
  title: string;
  body?: string;
  subredditId: string;
}

// Comment types
export interface Comment {
  id: string;
  content: string;
  postId: string;
  userId: string;
  parentCommentId?: string;
  createdAt: string;
  updatedAt: string;
  author: {
    id: string;
    username: string;
  };
  replies: Comment[];
}

export interface CreateCommentRequest {
  content: string;
  postId: string;
  parentCommentId?: string;
}

// Vote types
export interface Vote {
  id: string;
  userId: string;
  postId: string;
  value: 1 | -1;
  createdAt: string;
  updatedAt: string;
}

export interface VoteRequest {
  value: 1 | -1;
}

// API Response types
export interface ApiResponse<T> {
  data?: T;
  message?: string;
  error?: string;
  details?: string;
}

export interface PostWithComments {
  post: Post;
  comments: Comment[];
}

export interface SubredditWithPosts {
  subreddit: Subreddit;
  posts: Post[];
}

// UI State types
export interface LoadingState {
  isLoading: boolean;
  error: string | null;
}

export interface AuthState extends LoadingState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
}

// Form types
export interface FormErrors {
  [key: string]: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: FormErrors;
}

// Filter types
export interface PostFilters {
  sortBy: 'newest' | 'oldest' | 'popular' | 'controversial';
  timeRange: 'all' | 'today' | 'week' | 'month' | 'year';
  community?: string;
  searchQuery?: string;
}

export interface FilterOption {
  value: string;
  label: string;
}
