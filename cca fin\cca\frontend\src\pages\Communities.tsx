import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import { Container, Card, But<PERSON>, ErrorMessage, LoadingSpinner, theme } from '../styles/GlobalStyles';
import { Subreddit } from '../types';
import { apiService } from '../services/api';

const HomeContainer = styled(Container)`
  padding-top: ${theme.spacing.xl};
  padding-bottom: ${theme.spacing.xl};
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${theme.spacing.xl};
  
  @media (max-width: ${theme.breakpoints.sm}) {
    flex-direction: column;
    gap: ${theme.spacing.md};
    align-items: stretch;
  }
`;

const Title = styled.h1`
  color: ${theme.colors.textPrimary};
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
`;

const SubredditGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: ${theme.spacing.lg};
  margin-top: ${theme.spacing.lg};
`;

const SubredditCard = styled(Card)`
  padding: ${theme.spacing.lg};
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  cursor: pointer;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
`;

const SubredditName = styled.h3`
  color: ${theme.colors.textPrimary};
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 ${theme.spacing.sm} 0;
  
  &::before {
    content: 'r/';
    color: ${theme.colors.textMuted};
    font-weight: 400;
  }
`;

const SubredditDescription = styled.p`
  color: ${theme.colors.textSecondary};
  font-size: 0.875rem;
  line-height: 1.5;
  margin: 0 0 ${theme.spacing.md} 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
`;

const SubredditMeta = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
  color: ${theme.colors.textMuted};
`;

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${theme.spacing.xl};
  gap: ${theme.spacing.md};
  color: ${theme.colors.textSecondary};
`;

const EmptyState = styled.div`
  text-align: center;
  padding: ${theme.spacing.xl};
  color: ${theme.colors.textSecondary};
  
  h3 {
    color: ${theme.colors.textPrimary};
    margin-bottom: ${theme.spacing.md};
  }
  
  p {
    margin-bottom: ${theme.spacing.lg};
    line-height: 1.6;
  }
`;

const Communities: React.FC = () => {
  const [subreddits, setSubreddits] = useState<Subreddit[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchSubreddits();
  }, []);

  const fetchSubreddits = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await apiService.getSubreddits();
      setSubreddits(response.subreddits);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to load communities');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <HomeContainer>
        <LoadingContainer>
          <LoadingSpinner />
          <div>Loading communities...</div>
        </LoadingContainer>
      </HomeContainer>
    );
  }

  return (
    <HomeContainer>
      <Header>
        <Title>Communities</Title>
        <Button as={Link} to="/create-subreddit">
          Create Community
        </Button>
      </Header>

      {error && (
        <ErrorMessage>
          {error}
          <Button
            variant="ghost"
            size="sm"
            onClick={fetchSubreddits}
            style={{ marginLeft: theme.spacing.md }}
          >
            Try Again
          </Button>
        </ErrorMessage>
      )}

      {subreddits.length === 0 && !isLoading && !error ? (
        <EmptyState>
          <h3>No communities yet</h3>
          <p>
            Be the first to create a community and start building your corner of TalkHub!
          </p>
          <Button as={Link} to="/create-subreddit">
            Create the First Community
          </Button>
        </EmptyState>
      ) : (
        <SubredditGrid>
          {subreddits.map((subreddit) => (
            <SubredditCard
              key={subreddit.id}
              as={Link}
              to={`/r/${subreddit.name}`}
              style={{ textDecoration: 'none' }}
            >
              <SubredditName>{subreddit.name}</SubredditName>
              <SubredditDescription>
                {subreddit.description || 'No description available'}
              </SubredditDescription>
              <SubredditMeta>
                <span>Community</span>
                <span>Click to explore</span>
              </SubredditMeta>
            </SubredditCard>
          ))}
        </SubredditGrid>
      )}
    </HomeContainer>
  );
};

export default Communities;
