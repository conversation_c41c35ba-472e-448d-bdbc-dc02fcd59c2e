const { Subreddit, Post, User } = require('../models');

const createSubreddit = async (req, res) => {
  try {
    const { name, description } = req.body;

    const subreddit = new Subreddit({
      name,
      description
    });

    await subreddit.save();

    res.status(201).json({
      message: 'Subreddit created successfully',
      subreddit
    });
  } catch (error) {
    console.error('Create subreddit error:', error);
    if (error.code === 11000) {
      return res.status(400).json({
        error: 'Subreddit with this name already exists'
      });
    }
    res.status(500).json({
      error: 'Failed to create subreddit',
      details: error.message
    });
  }
};

const getAllSubreddits = async (req, res) => {
  try {
    const subreddits = await Subreddit.find()
      .sort({ createdAt: -1 });

    res.json({
      subreddits
    });
  } catch (error) {
    console.error('Get subreddits error:', error);
    res.status(500).json({
      error: 'Failed to fetch subreddits',
      details: error.message
    });
  }
};

const getSubredditPosts = async (req, res) => {
  try {
    const { name } = req.params;

    const subreddit = await Subreddit.findOne({ name });

    if (!subreddit) {
      return res.status(404).json({ error: 'Subreddit not found' });
    }

    const posts = await Post.find({ subredditId: subreddit._id })
      .populate('userId', 'username')
      .sort({ createdAt: -1 });

    res.json({
      subreddit,
      posts
    });
  } catch (error) {
    console.error('Get subreddit posts error:', error);
    res.status(500).json({
      error: 'Failed to fetch subreddit posts',
      details: error.message
    });
  }
};

module.exports = {
  createSubreddit,
  getAllSubreddits,
  getSubredditPosts
};