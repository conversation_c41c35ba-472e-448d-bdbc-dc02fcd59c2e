const express = require('express');
const { createPost, getAllPosts, getPostComments, voteOnPost } = require('../controllers/postController');
const auth = require('../middleware/auth');
const { validatePost, validateVote } = require('../middleware/validation');

const router = express.Router();

console.log('📝 Posts routes loaded successfully');

// Test route
router.get('/test', (req, res) => {
  res.json({ message: 'Posts routes are working!' });
});

// GET /api/posts (get all posts)
router.get('/', getAllPosts);

// POST /api/posts (create post) - requires auth
router.post('/', auth, validatePost, createPost);

// GET /api/posts/:id/comments
router.get('/:id/comments', getPostComments);

// POST /api/posts/:id/vote (upvote/downvote) - requires auth
router.post('/:id/vote', auth, validateVote, voteOnPost);

module.exports = router;