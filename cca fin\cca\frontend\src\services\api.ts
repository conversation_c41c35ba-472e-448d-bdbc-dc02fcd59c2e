import axios, { AxiosInstance, AxiosResponse } from 'axios';
import {
  AuthResponse,
  LoginRequest,
  RegisterRequest,
  Subreddit,
  CreateSubredditRequest,
  Post,
  CreatePostRequest,
  Comment,
  CreateCommentRequest,
  VoteRequest,
  PostWithComments,
  SubredditWithPosts,
} from '../types';

class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          // Clear token and redirect to login
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // Auth endpoints
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    const response: AxiosResponse<AuthResponse> = await this.api.post('/login', credentials);
    return response.data;
  }

  async register(userData: RegisterRequest): Promise<AuthResponse> {
    const response: AxiosResponse<AuthResponse> = await this.api.post('/register', userData);
    return response.data;
  }

  // Health check
  async healthCheck(): Promise<{ status: string; message: string; timestamp: string }> {
    const response = await this.api.get('/health');
    return response.data;
  }

  // Subreddit endpoints
  async getSubreddits(): Promise<{ subreddits: Subreddit[] }> {
    const response = await this.api.get('/subreddits');
    return response.data;
  }

  async createSubreddit(subredditData: CreateSubredditRequest): Promise<{ message: string; subreddit: Subreddit }> {
    const response = await this.api.post('/subreddits', subredditData);
    return response.data;
  }

  async getSubredditPosts(subredditName: string): Promise<SubredditWithPosts> {
    const response = await this.api.get(`/subreddits/${subredditName}/posts`);
    return response.data;
  }

  // Post endpoints
  async getAllPosts(): Promise<{ posts: Post[] }> {
    const response = await this.api.get('/posts');
    return response.data;
  }

  async createPost(postData: CreatePostRequest): Promise<{ message: string; post: Post }> {
    const response = await this.api.post('/posts', postData);
    return response.data;
  }

  // Admin endpoints
  async getAllUsersAdmin(): Promise<{ users: any[]; total: number }> {
    const response = await this.api.get('/admin/users');
    return response.data;
  }

  async getAllPostsAdmin(): Promise<{ posts: any[]; total: number }> {
    const response = await this.api.get('/admin/posts');
    return response.data;
  }

  async getAllSubredditsAdmin(): Promise<{ subreddits: any[]; total: number }> {
    const response = await this.api.get('/admin/subreddits');
    return response.data;
  }

  async deletePostAdmin(postId: string): Promise<{ message: string }> {
    const response = await this.api.delete(`/admin/posts/${postId}`);
    return response.data;
  }

  async deleteSubredditAdmin(subredditId: string): Promise<{ message: string }> {
    const response = await this.api.delete(`/admin/subreddits/${subredditId}`);
    return response.data;
  }

  async deleteUserAdmin(userId: string): Promise<{ message: string }> {
    const response = await this.api.delete(`/admin/users/${userId}`);
    return response.data;
  }

  async getPostWithComments(postId: string): Promise<PostWithComments> {
    const response = await this.api.get(`/posts/${postId}/comments`);
    return response.data;
  }

  async voteOnPost(postId: string, voteData: VoteRequest): Promise<{ message: string; vote?: any }> {
    const response = await this.api.post(`/posts/${postId}/vote`, voteData);
    return response.data;
  }

  // Comment endpoints
  async createComment(commentData: CreateCommentRequest): Promise<{ message: string; comment: Comment }> {
    const response = await this.api.post('/comments', commentData);
    return response.data;
  }
}

export const apiService = new ApiService();
export default apiService;
